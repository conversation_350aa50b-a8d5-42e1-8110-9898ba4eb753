<?php
session_start();

// Check if the user is logged in and is an admin
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    // Redirect unauthorized users to login page
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Initialize variables
$username = $email = $role = $full_name = $course = $year = $stream = "";
$username_err = $email_err = $role_err = $full_name_err = $course_err = $year_err = $stream_err = "";
$success_message = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    
    // Validate username
    if (empty(trim($_POST["username"]))) {
        $username_err = "Please enter a username.";
    } else {
        // Prepare a select statement
        $sql = "SELECT user_id FROM users WHERE email = ?";
        
        if ($stmt = mysqli_prepare($conn, $sql)) {
            // Bind variables to the prepared statement as parameters
            mysqli_stmt_bind_param($stmt, "s", $param_username);
            
            // Set parameters
            $param_username = trim($_POST["username"]);
            
            // Attempt to execute the prepared statement
            if (mysqli_stmt_execute($stmt)) {
                // Store result
                mysqli_stmt_store_result($stmt);
                
                if (mysqli_stmt_num_rows($stmt) == 1) {
                    $username_err = "This username is already taken.";
                } else {
                    $username = trim($_POST["username"]);
                }
            } else {
                echo "Oops! Something went wrong. Please try again later.";
            }

            // Close statement
            mysqli_stmt_close($stmt);
        }
    }
    
    // Validate email
    if (empty(trim($_POST["email"]))) {
        $email_err = "Please enter an email.";
    } else {
        $email = trim($_POST["email"]);
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $email_err = "Invalid email format.";
        }
    }
    
    // Validate role
    if (empty(trim($_POST["role"]))) {
        $role_err = "Please select a role.";
    } else {
        $role = trim($_POST["role"]);
    }
    
    // Validate full name
    if (empty(trim($_POST["full_name"]))) {
        $full_name_err = "Please enter a full name.";
    } else {
        $full_name = trim($_POST["full_name"]);
    }
    
    // Validate course, year, and stream for students
    if ($role === "student") {
        if (empty(trim($_POST["course"]))) {
            $course_err = "Please enter a course.";
        } else {
            $course = trim($_POST["course"]);
        }
        
        if (empty(trim($_POST["year"]))) {
            $year_err = "Please select a year.";
        } else {
            $year = trim($_POST["year"]);
        }
        
        if (empty(trim($_POST["stream"]))) {
            $stream_err = "Please select a stream.";
        } else {
            $stream = trim($_POST["stream"]);
        }
    }
    
    // Generate a random password
    $random_password = substr(md5(uniqid(mt_rand(), true)), 0, 8);
    $hashed_password = password_hash($random_password, PASSWORD_DEFAULT);
    
    // Check input errors before inserting into database
    if (empty($username_err) && empty($email_err) && empty($role_err) && empty($full_name_err) && 
        ($role !== "student" || (empty($course_err) && empty($year_err) && empty($stream_err)))) {
        
        // Prepare an insert statement
        $sql = "INSERT INTO users (email, password, role, full_name, course, year, stream) VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        if ($stmt = mysqli_prepare($conn, $sql)) {
            // Bind variables to the prepared statement as parameters
            mysqli_stmt_bind_param($stmt, "sssssss", $param_email, $param_password, $param_role, $param_full_name, $param_course, $param_year, $param_stream);
            
            // Set parameters
            $param_email = $email;
            $param_password = $hashed_password;
            $param_role = $role;
            $param_full_name = $full_name;
            $param_course = $course;
            $param_year = $year;
            $param_stream = $stream;
            
            // Attempt to execute the prepared statement
            if (mysqli_stmt_execute($stmt)) {
                // User created successfully
                $success_message = "User created successfully! Temporary password: " . $random_password;
                
                // Clear form data
                $username = $email = $role = $full_name = $course = $year = $stream = "";
            } else {
                echo "Oops! Something went wrong. Please try again later.";
            }

            // Close statement
            mysqli_stmt_close($stmt);
        }
    }
}

// Get courses for dropdown
$courses = [];
$sql = "SELECT DISTINCT course FROM users WHERE course IS NOT NULL AND course != '' ORDER BY course";
$result = mysqli_query($conn, $sql);
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $courses[] = $row['course'];
    }
}

// Get streams for dropdown
$streams = [];
$sql = "SELECT DISTINCT stream FROM users WHERE stream IS NOT NULL AND stream != '' ORDER BY stream";
$result = mysqli_query($conn, $sql);
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $streams[] = $row['stream'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Add New User | Admin Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-dark);
        }
        
        .container {
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            margin: 20px;
            transition: margin-left 0.3s ease;
        }
        
        #menuToggle:checked ~ .container {
            margin-left: 270px;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .page-header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .card-header {
            background: var(--primary-light);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 500;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(23, 88, 131, 0.2);
        }
        
        .form-control.is-invalid {
            border-color: var(--danger-color);
        }
        
        .invalid-feedback {
            color: var(--danger-color);
            font-size: 14px;
            margin-top: 5px;
        }
        
        .btn {
            padding: 12px 25px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            border: 1px solid var(--success-color);
            color: #27ae60;
        }
        
        .student-fields {
            display: none;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }
            
            .page-header {
                padding: 20px;
            }
            
            .page-header h1 {
                font-size: 24px;
            }
            
            #menuToggle:checked ~ .container {
                margin-left: 20px;
                opacity: 0.4;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/sidebar.php'); ?>

<div class="container">
    <div class="page-header">
        <h1>Add New User</h1>
        <p>Create a new user account for the venue management system.</p>
    </div>
    
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-header">
            <i class="fas fa-user-plus"></i> User Registration Form
        </div>
        <div class="card-body">
            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                <div class="form-group">
                    <label for="full_name">Full Name</label>
                    <input type="text" name="full_name" id="full_name" class="form-control <?php echo (!empty($full_name_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $full_name; ?>">
                    <?php if (!empty($full_name_err)): ?>
                        <div class="invalid-feedback"><?php echo $full_name_err; ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" name="email" id="email" class="form-control <?php echo (!empty($email_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $email; ?>">
                    <?php if (!empty($email_err)): ?>
                        <div class="invalid-feedback"><?php echo $email_err; ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" name="username" id="username" class="form-control <?php echo (!empty($username_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $username; ?>">
                    <?php if (!empty($username_err)): ?>
                        <div class="invalid-feedback"><?php echo $username_err; ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="role">Role</label>
                    <select name="role" id="role" class="form-control <?php echo (!empty($role_err)) ? 'is-invalid' : ''; ?>">
                        <option value="">Select Role</option>
                        <option value="admin" <?php echo ($role === "admin") ? "selected" : ""; ?>>Administrator</option>
                        <option value="lecturer" <?php echo ($role === "lecturer") ? "selected" : ""; ?>>Lecturer</option>
                        <option value="student" <?php echo ($role === "student") ? "selected" : ""; ?>>Student</option>
                    </select>
                    <?php if (!empty($role_err)): ?>
                        <div class="invalid-feedback"><?php echo $role_err; ?></div>
                    <?php endif; ?>
                </div>
                
                <div id="student-fields" class="student-fields">
                    <div class="form-group">
                        <label for="course">Course</label>
                        <input type="text" name="course" id="course" class="form-control <?php echo (!empty($course_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $course; ?>" list="course-list">
                        <datalist id="course-list">
                            <?php foreach ($courses as $course_option): ?>
                                <option value="<?php echo htmlspecialchars($course_option); ?>">
                            <?php endforeach; ?>
                        </datalist>
                        <?php if (!empty($course_err)): ?>
                            <div class="invalid-feedback"><?php echo $course_err; ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="year">Year</label>
                        <select name="year" id="year" class="form-control <?php echo (!empty($year_err)) ? 'is-invalid' : ''; ?>">
                            <option value="">Select Year</option>
                            <option value="1" <?php echo ($year === "1") ? "selected" : ""; ?>>Year 1</option>
                            <option value="2" <?php echo ($year === "2") ? "selected" : ""; ?>>Year 2</option>
                            <option value="3" <?php echo ($year === "3") ? "selected" : ""; ?>>Year 3</option>
                            <option value="4" <?php echo ($year === "4") ? "selected" : ""; ?>>Year 4</option>
                        </select>
                        <?php if (!empty($year_err)): ?>
                            <div class="invalid-feedback"><?php echo $year_err; ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="stream">Stream</label>
                        <input type="text" name="stream" id="stream" class="form-control <?php echo (!empty($stream_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $stream; ?>" list="stream-list">
                        <datalist id="stream-list">
                            <?php foreach ($streams as $stream_option): ?>
                                <option value="<?php echo htmlspecialchars($stream_option); ?>">
                            <?php endforeach; ?>
                        </datalist>
                        <?php if (!empty($stream_err)): ?>
                            <div class="invalid-feedback"><?php echo $stream_err; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="form-group">
                    <p><strong>Note:</strong> A temporary password will be generated automatically.</p>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Create User
                    </button>
                    <a href="users.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Users
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Show/hide student fields based on role selection
    document.addEventListener('DOMContentLoaded', function() {
        const roleSelect = document.getElementById('role');
        const studentFields = document.getElementById('student-fields');
        
        // Initial check
        if (roleSelect.value === 'student') {
            studentFields.style.display = 'block';
        }
        
        // Change event
        roleSelect.addEventListener('change', function() {
            if (this.value === 'student') {
                studentFields.style.display = 'block';
            } else {
                studentFields.style.display = 'none';
            }
        });
    });
</script>

</body>
</html>
