<?php
session_start();

// Check if the user is logged in and is an admin
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    // Redirect unauthorized users to login page
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Initialize variables
$venue_id = $venuename = $capacity = $location = $description = $features = "";
$venuename_err = $capacity_err = $location_err = "";
$success_message = $error_message = "";

// Get locations for datalist
$locations = [];
$location_sql = "SELECT DISTINCT location FROM venues WHERE location IS NOT NULL AND location != '' ORDER BY location";
$location_result = mysqli_query($conn, $location_sql);
if ($location_result) {
    while ($row = mysqli_fetch_assoc($location_result)) {
        $locations[] = $row['location'];
    }
}

// Check if venue ID is provided
if (isset($_GET['id']) && !empty($_GET['id'])) {
    $venue_id = $_GET['id'];
    
    // Check if form is submitted
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        // Validate venue name
        if (empty(trim($_POST["venuename"]))) {
            $venuename_err = "Please enter a venue name.";
        } else {
            $venuename = trim($_POST["venuename"]);
        }
        
        // Validate capacity
        if (empty(trim($_POST["capacity"]))) {
            $capacity_err = "Please enter the venue capacity.";
        } elseif (!is_numeric($_POST["capacity"]) || $_POST["capacity"] <= 0) {
            $capacity_err = "Capacity must be a positive number.";
        } else {
            $capacity = trim($_POST["capacity"]);
        }
        
        // Validate location
        if (empty(trim($_POST["location"]))) {
            $location_err = "Please enter the venue location.";
        } else {
            $location = trim($_POST["location"]);
        }
        
        // Get description
        $description = trim($_POST["description"]);
        
        // Process features
        $features_array = isset($_POST["features"]) ? $_POST["features"] : [];
        $features = implode(", ", $features_array);
        
        // Check if there are no errors
        if (empty($venuename_err) && empty($capacity_err) && empty($location_err)) {
            // Update venue in database
            $sql = "UPDATE venues SET venuename = ?, capacity = ?, location = ?, description = ?, features = ? WHERE venueid = ?";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "sisssi", $venuename, $capacity, $location, $description, $features, $venue_id);
            
            if (mysqli_stmt_execute($stmt)) {
                $success_message = "Venue updated successfully.";
            } else {
                $error_message = "Something went wrong. Please try again later.";
            }
            
            mysqli_stmt_close($stmt);
        }
    } else {
        // Get venue details
        $sql = "SELECT * FROM venues WHERE venueid = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $venue_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        if (mysqli_num_rows($result) === 1) {
            $venue = mysqli_fetch_assoc($result);
            $venuename = $venue['venuename'];
            $capacity = $venue['capacity'];
            $location = $venue['location'];
            $description = $venue['description'] ?? '';
            $features = $venue['features'] ?? '';
        } else {
            $error_message = "Venue not found.";
        }
    }
} else {
    $error_message = "Venue ID is required.";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Edit Venue | Admin Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-dark);
        }
        
        .container {
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            margin: 20px;
            transition: margin-left 0.3s ease;
        }
        
        #menuToggle:checked ~ .container {
            margin-left: 270px;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .page-header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .card-header {
            background: var(--primary-light);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 500;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(23, 88, 131, 0.2);
        }
        
        .is-invalid {
            border-color: var(--danger-color);
        }
        
        .invalid-feedback {
            color: var(--danger-color);
            font-size: 14px;
            margin-top: 5px;
        }
        
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
        }
        
        .checkbox-item input[type="checkbox"] {
            margin-right: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            margin-right: 10px;
        }
        
        .btn i {
            margin-right: 5px;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            border: 1px solid var(--success-color);
            color: #27ae60;
        }
        
        .alert-danger {
            background-color: rgba(231, 76, 60, 0.2);
            border: 1px solid var(--danger-color);
            color: #c0392b;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }
            
            .page-header {
                padding: 20px;
            }
            
            .page-header h1 {
                font-size: 24px;
            }
            
            .checkbox-group {
                grid-template-columns: 1fr;
            }
            
            #menuToggle:checked ~ .container {
                margin-left: 20px;
                opacity: 0.4;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/sidebar.php'); ?>

<div class="container">
    <div class="page-header">
        <h1>Edit Venue</h1>
        <p>Update venue information and settings.</p>
    </div>
    
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i classphp="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-header">
            <i class="fas fa-edit"></i> Edit Venue Information
        </div>
        <div class="card-body">
            <form action="edit_venue.php?id=<?php echo $venue_id; ?>" method="post">
                <div class="form-group">
                    <label for="venuename">Venue Name</label>
                    <input type="text" name="venuename" id="venuename" class="form-control <?php echo !empty($venuename_err) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($venuename); ?>">
                    <?php if (!empty($venuename_err)): ?>
                        <div class="invalid-feedback"><?php echo $venuename_err; ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="capacity">Capacity</label>
                    <input type="number" name="capacity" id="capacity" class="form-control <?php echo !empty($capacity_err) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($capacity); ?>">
                    <?php if (!empty($capacity_err)): ?>
                        <div class="invalid-feedback"><?php echo $capacity_err; ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="location">Location</label>
                    <input type="text" name="location" id="location" list="location-list" class="form-control <?php echo !empty($location_err) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($location); ?>">
                    <datalist id="location-list">
                        <?php foreach ($locations as $loc): ?>
                            <option value="<?php echo htmlspecialchars($loc); ?>">
                        <?php endforeach; ?>
                    </datalist>
                    <?php if (!empty($location_err)): ?>
                        <div class="invalid-feedback"><?php echo $location_err; ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea name="description" id="description" class="form-control" rows="4"><?php echo htmlspecialchars($description); ?></textarea>
                </div>
                
                <div class="form-group">
                    <label>Features</label>
                    <div class="checkbox-group">
                        <?php
                        $feature_options = [
                            'Projector', 'Whiteboard', 'Air Conditioning', 'Computer Lab',
                            'Smart Board', 'Wi-Fi', 'Audio System', 'Video Conferencing',
                            'Wheelchair Access', 'Podium', 'Microphone', 'Blackout Blinds'
                        ];
                        
                        $venue_features = explode(', ', $features);
                        foreach ($feature_options as $option):
                        ?>
                            <div class="checkbox-item">
                                <input type="checkbox" name="features[]" id="feature-<?php echo strtolower(str_replace(' ', '-', $option)); ?>" value="<?php echo $option; ?>" <?php echo in_array($option, $venue_features) ? 'checked' : ''; ?>>
                                <label for="feature-<?php echo strtolower(str_replace(' ', '-', $option)); ?>"><?php echo $option; ?></label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                    <a href="venues.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Venues
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // JavaScript to handle form validation and UI enhancements
    document.addEventListener('DOMContentLoaded', function() {
        // You can add client-side validation here if needed
    });
</script>

</body>
</html>
