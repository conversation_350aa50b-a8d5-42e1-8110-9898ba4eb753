-- Check if department column exists
SET @columnExists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'events'
    AND COLUMN_NAME = 'department'
);

-- Drop department column if it exists
SET @dropColumnSQL = IF(@columnExists > 0, 
                        'ALTER TABLE events DROP COLUMN department', 
                        'SELECT 1');
PREPARE stmt FROM @dropColumnSQL;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if course column exists
SET @courseExists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'events'
    AND COLUMN_NAME = 'course'
);

-- Add course column if it doesn't exist
SET @addCourseSQL = IF(@courseExists = 0, 
                      'ALTER TABLE events ADD COLUMN course VARCHAR(100) DEFAULT "All"', 
                      'SELECT 1');
PREPARE stmt FROM @addCourseSQL;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if year column exists
SET @yearExists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'events'
    AND COLUMN_NAME = 'year'
);

-- Add year column if it doesn't exist
SET @addYearSQL = IF(@yearExists = 0, 
                    'ALTER TABLE events ADD COLUMN year VARCHAR(10) DEFAULT "All"', 
                    'SELECT 1');
PREPARE stmt FROM @addYearSQL;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if stream column exists
SET @streamExists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'events'
    AND COLUMN_NAME = 'stream'
);

-- Add stream column if it doesn't exist
SET @addStreamSQL = IF(@streamExists = 0, 
                      'ALTER TABLE events ADD COLUMN stream VARCHAR(10) DEFAULT "All"', 
                      'SELECT 1');
PREPARE stmt FROM @addStreamSQL;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;