// Fetch recent issues reported
$check_issues_table = mysqli_query($conn, "SHOW TABLES LIKE 'issues'");
if (mysqli_num_rows($check_issues_table) > 0) {
    $sqlIssues = "SELECT i.*, v.venuename FROM issues i 
                  JOIN venues v ON i.venue_id = v.venueid 
                  ORDER BY i.reported_date DESC LIMIT 5";
    $resultIssues = mysqli_query($conn, $sqlIssues);
} else {
    // Create a dummy result set or set to null
    $resultIssues = null;
}

// Fetch recent user activities
$check_login_logs_table = mysqli_query($conn, "SHOW TABLES LIKE 'login_logs'");
if (mysqli_num_rows($check_login_logs_table) > 0) {
    $sqlActivities = "SELECT u.full_name, u.role, l.login_time 
                     FROM login_logs l 
                     JOIN users u ON l.user_id = u.user_id 
                     ORDER BY l.login_time DESC LIMIT 5";
    $resultActivities = mysqli_query($conn, $sqlActivities);
} else {
    // Create a dummy result set or set to null
    $resultActivities = null;
}