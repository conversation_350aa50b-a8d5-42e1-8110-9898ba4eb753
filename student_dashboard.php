<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "student") {
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Get user details
$user_id = $_SESSION["user"];
$user_query = "SELECT * FROM users WHERE user_id = ?";
$stmt = mysqli_prepare($conn, $user_query);
mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);
$user_result = mysqli_stmt_get_result($stmt);
$user_data = mysqli_fetch_assoc($user_result);

// Get recent check-ins
$checkins_query = "SELECT vc.*, v.venuename, v.location 
                  FROM venue_checkins vc 
                  JOIN venues v ON vc.venue_id = v.venueid 
                  WHERE vc.user_id = ? 
                  ORDER BY vc.check_in_time DESC 
                  LIMIT 5";
$stmt = mysqli_prepare($conn, $checkins_query);
mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);
$checkins_result = mysqli_stmt_get_result($stmt);

// Get upcoming events
$events_query = "SELECT * FROM events 
                WHERE (target_audience = 'all' 
                      OR (target_audience = 'specific' 
                          AND (target_course = 'all' OR target_course = ?) 
                          AND (target_year = 'all' OR target_year = ?) 
                          AND (target_stream = 'all' OR target_stream = ?)))
                AND event_date >= CURDATE() 
                ORDER BY event_date, start_time 
                LIMIT 3";
$stmt = mysqli_prepare($conn, $events_query);
mysqli_stmt_bind_param($stmt, "sss", $user_data['course'], $user_data['year'], $user_data['stream']);
mysqli_stmt_execute($stmt);
$events_result = mysqli_stmt_get_result($stmt);

// Get available venues
$venues_query = "SELECT * FROM venues WHERE status = 'available' LIMIT 5";
$venues_result = mysqli_query($conn, $venues_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Student Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 15px;
        }
        
        .welcome-header {
            background: linear-gradient(to right, var(--primary-color), var(--primary-light));
            color: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .welcome-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .welcome-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .dashboard-card-header {
            background: var(--primary-color);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .dashboard-card-header a {
            color: white;
            font-size: 14px;
            text-decoration: none;
            opacity: 0.8;
            transition: opacity 0.3s;
        }
        
        .dashboard-card-header a:hover {
            opacity: 1;
        }
        
        .dashboard-card-body {
            padding: 20px;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .action-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: var(--bg-light);
            border-radius: 8px;
            padding: 20px;
            text-decoration: none;
            color: var(--text-dark);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .action-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .action-button i {
            font-size: 24px;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .checkin-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .checkin-item:last-child {
            border-bottom: none;
        }
        
        .checkin-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .checkin-details {
            flex-grow: 1;
        }
        
        .checkin-venue {
            font-weight: 500;
            margin: 0 0 5px 0;
            color: var(--text-dark);
        }
        
        .checkin-time {
            font-size: 14px;
            color: #666;
            margin: 0;
        }
        
        .checkin-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }
        
        .status-active {
            background-color: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }
        
        .status-completed {
            background-color: rgba(52, 152, 219, 0.2);
            color: #2980b9;
        }
        
        .event-item {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .event-item:last-child {
            border-bottom: none;
        }
        
        .event-date {
            font-size: 14px;
            color: var(--primary-color);
            font-weight: 500;
            margin: 0 0 5px 0;
        }
        
        .event-title {
            font-weight: 500;
            margin: 0 0 5px 0;
            color: var(--text-dark);
        }
        
        .event-info {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #666;
            margin: 0;
        }
        
        .event-info i {
            margin-right: 5px;
            color: var(--primary-color);
        }
        
        .venue-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .venue-item:last-child {
            border-bottom: none;
        }
        
        .venue-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--success-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .venue-details {
            flex-grow: 1;
        }
        
        .venue-name {
            font-weight: 500;
            margin: 0 0 5px 0;
            color: var(--text-dark);
        }
        
        .venue-location {
            font-size: 14px;
            color: #666;
            margin: 0;
        }
        
        .empty-state {
            text-align: center;
            padding: 30px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 40px;
            color: #ddd;
            margin-bottom: 10px;
        }
        
        .empty-state p {
            margin: 0;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
                margin: 20px auto;
            }
            
            .welcome-header {
                padding: 20px;
            }
            
            .welcome-header h1 {
                font-size: 24px;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/studentsidebar.php'); ?>

<div class="container">
    <div class="welcome-header">
        <h1>Welcome, <?php echo $_SESSION["name"]; ?>!</h1>
        <p>Access your venues, events, and manage your student profile from this dashboard.</p>
    </div>
    
    <div class="dashboard-grid">
        <div class="dashboard-card">
            <div class="dashboard-card-header">
                <span><i class="fas fa-bolt"></i> Quick Actions</span>
            </div>
            <div class="dashboard-card-body">
                <div class="quick-actions">
                    <a href="free_venues.php" class="action-button">
                        <i class="fas fa-door-open"></i>
                        <span>Free Venues</span>
                    </a>
                    <a href="events.php" class="action-button">
                        <i class="fas fa-calendar-alt"></i>
                        <span>View Events</span>
                    </a>
                    <a href="student_schedule.php" class="action-button">
                        <i class="fas fa-calendar-day"></i>
                        <span>Class Schedule</span>
                    </a>
                    <a href="profile.php" class="action-button">
                        <i class="fas fa-user-circle"></i>
                        <span>My Profile</span>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="dashboard-card">
            <div class="dashboard-card-header">
                <span><i class="fas fa-calendar-alt"></i> Upcoming Events</span>
                <a href="events.php">View All</a>
            </div>
            <div class="dashboard-card-body">
                <?php if (mysqli_num_rows($events_result) > 0): ?>
                    <?php while ($event = mysqli_fetch_assoc($events_result)): ?>
                        <div class="event-item">
                            <p class="event-date">
                                <?php echo date('l, M d, Y', strtotime($event['event_date'])); ?>
                            </p>
                            <h4 class="event-title"><?php echo htmlspecialchars($event['event_title']); ?></h4>
                            <p class="event-info">
                                <i class="fas fa-clock"></i>
                                <?php 
                                    echo date('g:i A', strtotime($event['start_time'])); 
                                    echo ' - '; 
                                    echo date('g:i A', strtotime($event['end_time']));
                                ?>
                            </p>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-calendar-times"></i>
                        <p>No upcoming events</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="dashboard-grid">
        <div class="dashboard-card">
            <div class="dashboard-card-header">
                <span><i class="fas fa-calendar-day"></i> Today's Classes</span>
                <a href="student_schedule.php">View Schedule</a>
            </div>
            <div class="dashboard-card-body">
                <?php 
                // Get today's classes
                $today = date('l'); // Current day name (Monday, Tuesday, etc.)
                $classes_query = "SELECT * FROM timetables 
                                 WHERE course = ? AND year = ? AND stream = ? AND day = ?
                                 ORDER BY start_time ASC";
                $stmt = mysqli_prepare($conn, $classes_query);
                mysqli_stmt_bind_param($stmt, "siss", $user_data['course'], $user_data['year'], $user_data['stream'], $today);
                mysqli_stmt_execute($stmt);
                $classes_result = mysqli_stmt_get_result($stmt);
                ?>
                
                <?php if (mysqli_num_rows($classes_result) > 0): ?>
                    <?php while ($class = mysqli_fetch_assoc($classes_result)): ?>
                        <div class="class-item">
                            <div class="class-time">
                                <div><?php echo date("h:i A", strtotime($class['start_time'])); ?></div>
                                <div><?php echo date("h:i A", strtotime($class['end_time'])); ?></div>
                            </div>
                            <div class="class-details">
                                <div class="class-subject"><?php echo htmlspecialchars($class['subject_code']); ?></div>
                                <div class="class-info">
                                    <span><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($class['venue']); ?></span>
                                    <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($class['lecturer']); ?></span>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-calendar-times"></i>
                        <p>You have no classes scheduled for today.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="dashboard-card">
            <div class="dashboard-card-header">
                <span><i class="fas fa-bell"></i> Notifications</span>
                <a href="notifications.php">View All</a>
            </div>
            <div class="dashboard-card-body">
                <?php 
                // Get notifications
                $notifications_query = "SELECT * FROM notifications 
                                       WHERE user_id = ?
                                       ORDER BY created_at DESC LIMIT 5";
                $stmt = mysqli_prepare($conn, $notifications_query);
                mysqli_stmt_bind_param($stmt, "i", $user_id);
                mysqli_stmt_execute($stmt);
                $notifications_result = mysqli_stmt_get_result($stmt);
                ?>
                
                <?php if (mysqli_num_rows($notifications_result) > 0): ?>
                    <?php while ($notification = mysqli_fetch_assoc($notifications_result)): ?>
                        <div class="notification-item">
                            <div class="notification-title">
                                <?php echo htmlspecialchars($notification['title']); ?>
                            </div>
                            <div class="notification-time">
                                <?php echo date('M d, g:i A', strtotime($notification['created_at'])); ?>
                            </div>
                            <div class="notification-content">
                                <?php echo htmlspecialchars($notification['message']); ?>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-bell-slash"></i>
                        <p>No notifications at this time</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="dashboard-grid">
        <div class="dashboard-card">
            <div class="dashboard-card-header">
                <span><i class="fas fa-building"></i> Available Venues</span>
                <a href="venues.php">View All</a>
            </div>
            <div class="dashboard-card-body">
                <?php if (mysqli_num_rows($venues_result) > 0): ?>
                    <?php while ($venue = mysqli_fetch_assoc($venues_result)): ?>
                        <div class="venue-item">
                            <div class="venue-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="venue-details">
                                <h4 class="venue-name"><?php echo htmlspecialchars($venue['venuename']); ?></h4>
                                <p class="venue-location">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <?php echo htmlspecialchars($venue['location']); ?>
                                </p>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-info-circle"></i>
                        <p>No available venues found</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
    // Add active class to current page link
    document.addEventListener('DOMContentLoaded', function() {
        const currentPage = window.location.pathname.split('/').pop();
        const menuLinks = document.querySelectorAll('#menu a');
        
        menuLinks.forEach(link => {
            const linkPage = link.getAttribute('href');
            if (linkPage === currentPage) {
                link.classList.add('active');
            }
        });
    });
</script>

</body>
</html>
