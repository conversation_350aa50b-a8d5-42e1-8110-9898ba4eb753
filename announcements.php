<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Get user's course, year, and stream for filtering announcements
$user_id = $_SESSION["user"];
$user_query = "SELECT course, year, stream FROM users WHERE user_id = ?";
$stmt = mysqli_prepare($conn, $user_query);
mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);
$user_result = mysqli_stmt_get_result($stmt);
$user_data = mysqli_fetch_assoc($user_result);

// Get announcements relevant to the user
$query = "SELECT a.*, u.full_name as posted_by 
          FROM announcements a 
          JOIN users u ON a.posted_by = u.user_id 
          WHERE (a.target_audience = 'all' 
                OR (a.target_course = ? OR a.target_course = 'all')
                AND (a.target_year = ? OR a.target_year = 'all')
                AND (a.target_stream = ? OR a.target_stream = 'all'))
          AND a.expiry_date >= CURDATE()
          ORDER BY a.posted_date DESC";

$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, "sis", $user_data['course'], $user_data['year'], $user_data['stream']);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Announcements | Student Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 15px;
        }
        
        h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 24px;
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .announcement-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .announcement {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .announcement-header {
            background: var(--primary-color);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .announcement-title {
            font-size: 18px;
            font-weight: 500;
            margin: 0;
        }
        
        .announcement-date {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .announcement-body {
            padding: 20px;
            color: var(--text-dark);
        }
        
        .announcement-content {
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .announcement-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid #eee;
            font-size: 14px;
            color: #777;
        }
        
        .announcement-author {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .announcement-author i {
            color: var(--primary-color);
        }
        
        .announcement-target {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .announcement-target i {
            color: var(--primary-color);
        }
        
        .announcement-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            background: rgba(23, 88, 131, 0.1);
            color: var(--primary-color);
            margin-right: 5px;
            font-size: 12px;
        }
        
        .no-announcements {
            background: white;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .no-announcements i {
            font-size: 48px;
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .no-announcements h3 {
            margin: 0 0 10px 0;
            color: var(--text-dark);
        }
        
        .no-announcements p {
            color: #777;
            margin: 0;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
                margin: 20px auto;
            }
            
            .announcement-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            
            .announcement-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/studentsidebar.php'); ?>

<div class="container">
    <h2><i class="fas fa-bullhorn"></i> Announcements</h2>
    
    <?php if (mysqli_num_rows($result) > 0): ?>
        <div class="announcement-list">
            <?php while ($row = mysqli_fetch_assoc($result)): ?>
                <div class="announcement">
                    <div class="announcement-header">
                        <h3 class="announcement-title"><?php echo htmlspecialchars($row['title']); ?></h3>
                        <span class="announcement-date">
                            <i class="far fa-calendar-alt"></i> 
                            <?php echo date('M d, Y', strtotime($row['posted_date'])); ?>
                        </span>
                    </div>
                    <div class="announcement-body">
                        <div class="announcement-content">
                            <?php echo nl2br(htmlspecialchars($row['content'])); ?>
                        </div>
                        <div class="announcement-meta">
                            <div class="announcement-author">
                                <i class="fas fa-user"></i>
                                <span>Posted by: <?php echo htmlspecialchars($row['posted_by']); ?></span>
                            </div>
                            <div class="announcement-target">
                                <i class="fas fa-users"></i>
                                <span>For: 
                                    <?php if ($row['target_audience'] === 'all'): ?>
                                        <span class="announcement-badge">Everyone</span>
                                    <?php else: ?>
                                        <?php if ($row['target_course'] !== 'all'): ?>
                                            <span class="announcement-badge"><?php echo htmlspecialchars($row['target_course']); ?></span>
                                        <?php endif; ?>
                                        
                                        <?php if ($row['target_year'] !== 'all'): ?>
                                            <span class="announcement-badge">Year <?php echo htmlspecialchars($row['target_year']); ?></span>
                                        <?php endif; ?>
                                        
                                        <?php if ($row['target_stream'] !== 'all'): ?>
                                            <span class="announcement-badge">Stream <?php echo htmlspecialchars($row['target_stream']); ?></span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>
    <?php else: ?>
        <div class="no-announcements">
            <i class="fas fa-bullhorn"></i>
            <h3>No Announcements</h3>
            <p>There are no current announcements for you.</p>
        </div>
    <?php endif; ?>
</div>

<script>
    // Add active class to current page link
    document.addEventListener('DOMContentLoaded', function() {
        const currentPage = window.location.pathname.split('/').pop();
        const menuLinks = document.querySelectorAll('#menu a');
        
        menuLinks.forEach(link => {
            const linkPage = link.getAttribute('href');
            if (linkPage === currentPage) {
                link.classList.add('active');
            }
        });
    });
</script>

</body>
</html>