<?php
session_start();

// Check if the user is logged in and is a lecturer
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "lecturer") {
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Get user details
$user_id = $_SESSION["user"];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Scan Venue | Lecturer Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 15px;
        }
        
        h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 24px;
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .scan-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
        }
        
        .scan-header {
            margin-bottom: 20px;
        }
        
        .scan-header p {
            color: var(--text-dark);
            margin-bottom: 20px;
        }
        
        #qr-reader {
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
            border: 2px solid var(--primary-light);
            border-radius: 8px;
            overflow: hidden;
        }
        
        #qr-reader__status_span {
            background: var(--primary-color) !important;
            color: white !important;
        }
        
        #qr-reader__dashboard_section_csr button {
            background: var(--primary-color) !important;
            color: white !important;
            border: none !important;
            padding: 10px 15px !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            margin: 10px 0 !important;
        }
        
        .manual-entry {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .manual-entry h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
            text-decoration: none;
        }
        
        .btn:hover {
            background-color: var(--primary-dark);
        }
        
        .btn-block {
            display: block;
            width: 100%;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }
        
        .alert-danger {
            background-color: rgba(231, 76, 60, 0.2);
            color: #c0392b;
        }
        
        #result-container {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background-color: rgba(46, 204, 113, 0.1);
            border-radius: 8px;
        }
        
        #error-container {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background-color: rgba(231, 76, 60, 0.1);
            border-radius: 8px;
        }
    </style>
    <!-- Include the HTML5 QR Code Scanner library -->
    <script src="https://unpkg.com/html5-qrcode"></script>
</head>
<body>

<?php include('CSS/lecturersidebar.php'); ?>

<div class="container">
    <h2><i class="fas fa-qrcode"></i> Scan Venue QR Code</h2>
    
    <div class="scan-container">
        <div class="scan-header">
            <p>Position the QR code within the scanner to check in to a venue as a lecturer.</p>
            <p class="info-text">Once you check in, students will be able to mark their attendance at this venue.</p>
        </div>
        
        <div id="qr-reader"></div>
        
        <div id="result-container">
            <h3>Success!</h3>
            <p id="result-message"></p>
            <a href="lecturer_dashboard.php" class="btn">Return to Dashboard</a>
        </div>
        
        <div id="error-container">
            <h3>Error</h3>
            <p id="error-message"></p>
            <button onclick="resetScanner()" class="btn">Try Again</button>
        </div>
        
        <div class="manual-entry">
            <h3>Manual Entry</h3>
            <p>If scanning doesn't work, you can enter the venue code manually:</p>
            
            <form id="manual-form" method="post" action="process_venue_scan.php">
                <div class="form-group">
                    <label for="venue_code">Venue Code</label>
                    <input type="text" id="venue_code" name="venue_code" class="form-control" placeholder="Enter venue code" required>
                </div>
                
                <button type="submit" class="btn btn-block">Check In</button>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add active class to current page link
        const currentPage = window.location.pathname.split('/').pop();
        const menuLinks = document.querySelectorAll('#menu a');
        
        menuLinks.forEach(link => {
            const linkPage = link.getAttribute('href');
            if (linkPage === currentPage) {
                link.classList.add('active');
            }
        });
        
        // Initialize QR scanner
        const html5QrCode = new Html5Qrcode("qr-reader");
        const qrConfig = { fps: 10, qrbox: 250 };
        
        // Start scanner
        html5QrCode.start(
            { facingMode: "environment" },
            qrConfig,
            onScanSuccess,
            onScanFailure
        );
        
        function onScanSuccess(decodedText, decodedResult) {
            // Stop scanner
            html5QrCode.stop();
            
            // Process the scanned QR code
            processQrCode(decodedText);
        }
        
        function onScanFailure(error) {
            // Handle scan failure silently
            console.warn(`QR scan error: ${error}`);
        }
        
        function processQrCode(qrData) {
            console.log("QR Data received:", qrData);
            
            // Try to parse as JSON first
            let venueData;
            try {
                venueData = JSON.parse(qrData);
                console.log("Parsed JSON data:", venueData);
            } catch (e) {
                console.log("Not valid JSON, using as raw data");
                // Not JSON, use as is
                venueData = qrData;
            }
            
            // Prepare data for sending
            let formData;
            if (typeof venueData === 'object' && venueData.venueid) {
                formData = `venue_id=${encodeURIComponent(venueData.venueid)}`;
            } else {
                formData = `venue_code=${encodeURIComponent(qrData)}`;
            }
            
            console.log("Sending form data:", formData);
            
            // Send the QR data to the server for processing
            fetch('process_venue_scan.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: formData
            })
            .then(response => {
                console.log("Response status:", response.status);
                return response.json();
            })
            .then(data => {
                console.log("Response data:", data);
                if (data.success) {
                    // Show success message
                    document.getElementById('result-message').textContent = data.message;
                    document.getElementById('result-container').style.display = 'block';
                    document.getElementById('qr-reader').style.display = 'none';
                } else {
                    // Show error message
                    document.getElementById('error-message').textContent = data.message;
                    document.getElementById('error-container').style.display = 'block';
                    document.getElementById('qr-reader').style.display = 'none';
                }
            })
            .catch(error => {
                console.error("Fetch error:", error);
                // Show error message
                document.getElementById('error-message').textContent = 'An error occurred while processing the QR code: ' + error.message;
                document.getElementById('error-container').style.display = 'block';
                document.getElementById('qr-reader').style.display = 'none';
            });
        }
        
        // Function to reset the scanner
        window.resetScanner = function() {
            document.getElementById('error-container').style.display = 'none';
            document.getElementById('qr-reader').style.display = 'block';
            
            html5QrCode.start(
                { facingMode: "environment" },
                qrConfig,
                onScanSuccess,
                onScanFailure
            );
        };
    });
</script>

</body>
</html>


