<?php
session_start();

// Check if the user is logged in and is an admin
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    // Redirect unauthorized users to login page
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Initialize variables
$success_message = $error_message = "";
$settings = [];

// Get current settings
$sql = "SELECT * FROM system_settings";
$result = mysqli_query($conn, $sql);

if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Update system settings
    $updated = true;
    
    // Site settings
    $site_name = trim($_POST['site_name']);
    $site_description = trim($_POST['site_description']);
    $admin_email = trim($_POST['admin_email']);
    
    // Security settings
    $max_login_attempts = (int)$_POST['max_login_attempts'];
    $password_reset_expiry = (int)$_POST['password_reset_expiry'];
    $session_timeout = (int)$_POST['session_timeout'];
    
    // Notification settings
    $enable_email_notifications = isset($_POST['enable_email_notifications']) ? 1 : 0;
    $enable_browser_notifications = isset($_POST['enable_browser_notifications']) ? 1 : 0;
    
    // Venue settings
    $default_checkout_time = (int)$_POST['default_checkout_time'];
    $auto_free_after_hours = (int)$_POST['auto_free_after_hours'];
    
    // Update each setting in the database
    $settings_to_update = [
        'site_name' => $site_name,
        'site_description' => $site_description,
        'admin_email' => $admin_email,
        'max_login_attempts' => $max_login_attempts,
        'password_reset_expiry' => $password_reset_expiry,
        'session_timeout' => $session_timeout,
        'enable_email_notifications' => $enable_email_notifications,
        'enable_browser_notifications' => $enable_browser_notifications,
        'default_checkout_time' => $default_checkout_time,
        'auto_free_after_hours' => $auto_free_after_hours
    ];
    
    foreach ($settings_to_update as $key => $value) {
        $update_sql = "INSERT INTO system_settings (setting_key, setting_value) 
                      VALUES (?, ?) 
                      ON DUPLICATE KEY UPDATE setting_value = ?";
        $stmt = mysqli_prepare($conn, $update_sql);
        mysqli_stmt_bind_param($stmt, "sss", $key, $value, $value);
        
        if (!mysqli_stmt_execute($stmt)) {
            $updated = false;
            $error_message = "Failed to update settings. Please try again.";
            break;
        }
        
        // Update local settings array
        $settings[$key] = $value;
    }
    
    if ($updated) {
        $success_message = "System settings updated successfully.";
    }
}

// Set default values if not in database
$defaults = [
    'site_name' => 'Venue Management System',
    'site_description' => 'Manage university venues efficiently',
    'admin_email' => '<EMAIL>',
    'max_login_attempts' => 5,
    'password_reset_expiry' => 24,
    'session_timeout' => 30,
    'enable_email_notifications' => 1,
    'enable_browser_notifications' => 1,
    'default_checkout_time' => 2,
    'auto_free_after_hours' => 12
];

foreach ($defaults as $key => $value) {
    if (!isset($settings[$key])) {
        $settings[$key] = $value;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>System Settings | Admin Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-dark);
        }
        
        .container {
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            margin: 20px;
            transition: margin-left 0.3s ease;
        }
        
        #menuToggle:checked ~ .container {
            margin-left: 270px;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .page-header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .card-header {
            background: var(--primary-light);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 500;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(23, 88, 131, 0.2);
        }
        
        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .form-check-input {
            margin-right: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            margin-right: 10px;
        }
        
        .btn i {
            margin-right: 5px;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-danger {
            background: var(--danger-color);
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            border: 1px solid var(--success-color);
            color: #27ae60;
        }
        
        .alert-danger {
            background-color: rgba(231, 76, 60, 0.2);
            border: 1px solid var(--danger-color);
            color: #c0392b;
        }
        
        .settings-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .settings-tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .settings-tab.active {
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .settings-content {
            display: none;
        }
        
        .settings-content.active {
            display: block;
        }
        
        .input-group {
            display: flex;
            align-items: center;
        }
        
        .input-group .form-control {
            flex: 1;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
        
        .input-group-append {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            background-color: #e9ecef;
            border: 1px solid #ddd;
            border-left: none;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }
            
            .page-header {
                padding: 20px;
            }
            
            .page-header h1 {
                font-size: 24px;
            }
            
            .settings-tabs {
                flex-direction: column;
            }
            
            .settings-tab {
                padding: 10px;
                border-bottom: 1px solid #ddd;
            }
            
            .settings-tab.active {
                border-bottom: 1px solid var(--primary-color);
                border-left: 3px solid var(--primary-color);
            }
            
            #menuToggle:checked ~ .container {
                margin-left: 20px;
                opacity: 0.4;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/sidebar.php'); ?>

<div class="container">
    <div class="page-header">
        <h1>System Settings</h1>
        <p>Configure and customize your venue management system.</p>
    </div>
    
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-header">
            <i class="fas fa-cogs"></i> Settings Configuration
        </div>
        <div class="card-body">
            <div class="settings-tabs">
                <div class="settings-tab active" data-tab="general">
                    <i class="fas fa-globe"></i> General
                </div>
                <div class="settings-tab" data-tab="security">
                    <i class="fas fa-shield-alt"></i> Security
                </div>
                <div class="settings-tab" data-tab="notifications">
                    <i class="fas fa-bell"></i> Notifications
                </div>
                <div class="settings-tab" data-tab="venues">
                    <i class="fas fa-building"></i> Venues
                </div>
            </div>
            
            <form action="system_settings.php" method="post">
                <!-- General Settings -->
                <div class="settings-content active" id="general-settings">
                    <div class="form-group">
                        <label for="site_name">Site Name</label>
                        <input type="text" name="site_name" id="site_name" class="form-control" value="<?php echo htmlspecialchars($settings['site_name']); ?>">
                        <small class="text-muted">The name of your venue management system.</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="site_description">Site Description</label>
                        <textarea name="site_description" id="site_description" class="form-control" rows="3"><?php echo htmlspecialchars($settings['site_description']); ?></textarea>
                        <small class="text-muted">A brief description of your venue management system.</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_email">Admin Email</label>
                        <input type="email" name="admin_email" id="admin_email" class="form-control" value="<?php echo htmlspecialchars($settings['admin_email']); ?>">
                        <small class="text-muted">The primary email address for system notifications.</small>
                    </div>
                </div>
                
                <!-- Security Settings -->
                <div class="settings-content" id="security-settings">
                    <div class="form-group">
                        <label for="max_login_attempts">Maximum Login Attempts</label>
                        <input type="number" name="max_login_attempts" id="max_login_attempts" class="form-control" value="<?php echo htmlspecialchars($settings['max_login_attempts']); ?>" min="1" max="10">
                        <small class="text-muted">Number of failed login attempts before account lockout.</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="password_reset_expiry">Password Reset Link Expiry</label>
                        <div class="input-group">
                            <input type="number" name="password_reset_expiry" id="password_reset_expiry" class="form-control" value="<?php echo htmlspecialchars($settings['password_reset_expiry']); ?>" min="1" max="72">
                            <div class="input-group-append">
                                hours
                            </div>
                        </div>
                        <small class="text-muted">How long password reset links remain valid.</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="session_timeout">Session Timeout</label>
                        <div class="input-group">
                            <input type="number" name="session_timeout" id="session_timeout" class="form-control" value="<?php echo htmlspecialchars($settings['session_timeout']); ?>" min="5" max="120">
                            <div class="input-group-append">
                                minutes
                            </div>
                        </div>
                        <small class="text-muted">How long user sessions remain active without activity.</small>
                    </div>
                </div>
                
                <!-- Notification Settings -->
                <div class="settings-content" id="notifications-settings">
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" name="enable_email_notifications" id="enable_email_notifications" class="form-check-input" <?php echo $settings['enable_email_notifications'] ? 'checked' : ''; ?>>
                            <label for="enable_email_notifications" class="form-check-label">Enable Email Notifications</label>
                        </div>
                        <small class="text-muted">Send email notifications for important system events.</small>
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" name="enable_browser_notifications" id="enable_browser_notifications" class="form-check-input" <?php echo $settings['enable_browser_notifications'] ? 'checked' : ''; ?>>
                            <label for="enable_browser_notifications" class="form-check-label">Enable Browser Notifications</label>
                        </div>
                        <small class="text-muted">Show browser notifications for real-time alerts.</small>
                    </div>
                </div>
                
                <!-- Venue Settings -->
                <div class="settings-content" id="venues-settings">
                    <div class="form-group">
                        <label for="default_checkout_time">Default Checkout Time</label>
                        <div class="input-group">
                            <input type="number" name="default_checkout_time" id="default_checkout_time" class="form-control" value="<?php echo htmlspecialchars($settings['default_checkout_time']); ?>" min="1" max="24">
                            <div class="input-group-append">
                                hours
                            </div>
                        </div>
                        <small class="text-muted">Default duration before automatic checkout if user forgets to check out.</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="auto_free_after_hours">Auto-Free Venue After</label>
                        <div class="input-group">
                            <input type="number" name="auto_free_after_hours" id="auto_free_after_hours" class="form-control" value="<?php echo htmlspecialchars($settings['auto_free_after_hours']); ?>" min="1" max="48">
                            <div class="input-group-append">
                                hours
                            </div>
                        </div>
                        <small class="text-muted">Automatically mark venues as free after this many hours of inactivity.</small>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <i class="fas fa-database"></i> Database Maintenance
        </div>
        <div class="card-body">
            <p>Perform database maintenance operations to optimize system performance.</p>
            <div class="action-buttons">
                <a href="backup_database.php" class="btn btn-primary">
                    <i class="fas fa-download"></i> Backup Database
                </a>
                <a href="clear_logs.php" class="btn btn-warning">
                    <i class="fas fa-broom"></i> Clear System Logs
                </a>
                <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#resetModal">
                    <i class="fas fa-exclamation-triangle"></i> Reset System
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Reset System Modal -->
<div class="modal fade" id="resetModal" tabindex="-1" role="dialog" aria-labelledby="resetModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resetModalLabel">Confirm System Reset</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p class="text-danger">Warning: This action will reset the entire system to its default state. All data will be lost.</p>
                <p>Please type "RESET" to confirm:</p>
                <input type="text" id="resetConfirmation" class="form-control">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmResetBtn" disabled>Reset System</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tab switching
        const tabs = document.querySelectorAll('.settings-tab');
        const contents = document.querySelectorAll('.settings-content');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const target = tab.getAttribute('data-tab');
                
                // Remove active class from all tabs and contents
                tabs.forEach(t => t.classList.remove('active'));
                contents.forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                document.getElementById(`${target}-settings`).classList.add('active');
            });
        });
        
        // Reset confirmation
        const resetInput = document.getElementById('resetConfirmation');
        const resetBtn = document.getElementById('confirmResetBtn');
        
        resetInput.addEventListener('input', function() {
            resetBtn.disabled = this.value !== 'RESET';
        });
        
        resetBtn.addEventListener('click', function() {
            if (resetInput.value === 'RESET') {
                window.location.href = 'reset_system.php';
            }
        });
    });
</script>

</body>
</html>