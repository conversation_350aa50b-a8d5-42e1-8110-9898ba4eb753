-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS project;
USE project;

-- STEP 1: Create all tables WITHOUT foreign keys

-- Users table
CREATE TABLE IF NOT EXISTS users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    full_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'student',
    course VARCHAR(50) DEFAULT NULL,
    year INT DEFAULT NULL,
    stream VARCHAR(10) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Venues table
CREATE TABLE IF NOT EXISTS venues (
    venueid INT AUTO_INCREMENT PRIMARY KEY,
    venuename VARCHAR(255) NOT NULL,
    qrcode VARCHAR(255) NOT NULL,
    venuedescription TEXT DEFAULT NULL,
    status ENUM('free','occupied') NOT NULL DEFAULT 'free'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Timetables table
CREATE TABLE IF NOT EXISTS timetables (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course VARCHAR(50) DEFAULT NULL,
    year INT DEFAULT NULL,
    stream VARCHAR(10) DEFAULT NULL,
    day VARCHAR(10) DEFAULT NULL,
    start_time TIME DEFAULT NULL,
    end_time TIME DEFAULT NULL,
    type VARCHAR(30) DEFAULT NULL,
    subject_code VARCHAR(50) DEFAULT NULL,
    venue VARCHAR(50) DEFAULT NULL,
    lecturer VARCHAR(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Password reset requests table
CREATE TABLE IF NOT EXISTS password_reset_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    expires_at DATETIME NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    used TINYINT(1) NOT NULL DEFAULT 0,
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Venue check-ins table
CREATE TABLE IF NOT EXISTS venue_checkins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    venue_id INT NOT NULL,
    check_in_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    check_out_time DATETIME DEFAULT NULL,
    purpose VARCHAR(255) DEFAULT NULL,
    KEY `user_id` (`user_id`),
    KEY `venue_id` (`venue_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Reports table
CREATE TABLE IF NOT EXISTS reports (
    report_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    venue_id INT,
    description TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    reported_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    KEY `user_id` (`user_id`),
    KEY `venue_id` (`venue_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- STEP 2: Add foreign key constraints after all tables exist

-- Add foreign keys to venue_checkins
ALTER TABLE venue_checkins
ADD CONSTRAINT `venue_checkins_ibfk_1` 
FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;

ALTER TABLE venue_checkins
ADD CONSTRAINT `venue_checkins_ibfk_2` 
FOREIGN KEY (`venue_id`) REFERENCES `venues` (`venueid`) ON DELETE CASCADE;

-- Add foreign keys to reports
ALTER TABLE reports
ADD CONSTRAINT `reports_ibfk_1` 
FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE SET NULL;

ALTER TABLE reports
ADD CONSTRAINT `reports_ibfk_2` 
FOREIGN KEY (`venue_id`) REFERENCES `venues` (`venueid`) ON DELETE SET NULL;

