-- First, create the table without foreign keys
CREATE TABLE IF NOT EXISTS venue_checkins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    venue_id INT NOT NULL,
    check_in_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    check_out_time DATETIME DEFAULT NULL,
    purpose VARCHAR(255) DEFAULT NULL,
    KEY `user_id` (`user_id`),
    <PERSON><PERSON>Y `venue_id` (`venue_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Then add the foreign keys separately
ALTER TABLE venue_checkins
ADD CONSTRAINT `venue_checkins_ibfk_1` 
FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;

ALTER TABLE venue_checkins
ADD CONSTRAINT `venue_checkins_ibfk_2` 
FOREIGN KEY (`venue_id`) REFERENCES `venues` (`venueid`) ON DELETE CASCADE;
