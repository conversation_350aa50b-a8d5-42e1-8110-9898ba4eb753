<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "student") {
    // Redirect unauthorized users to another page, maybe the dashboard or homepage
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Initialize search variables
$search_term = "";
$search_condition = "";

// Handle search
if (isset($_POST['search'])) {
    $search_term = mysqli_real_escape_string($conn, $_POST['search_term']);
    $search_condition = " AND (venuename LIKE '%$search_term%' OR venuedescription LIKE '%$search_term%')";
}

// Clear search
if (isset($_POST['clear_search'])) {
    $search_term = "";
    $search_condition = "";
}

// Check if the category column exists in the venues table
$check_category_sql = "SHOW COLUMNS FROM venues LIKE 'category'";
$category_exists = mysqli_query($conn, $check_category_sql);

// Get venue categories for filter if the column exists
$categories = [];
if (mysqli_num_rows($category_exists) > 0) {
    $sql_categories = "SELECT DISTINCT category FROM venues ORDER BY category";
    $result_categories = mysqli_query($conn, $sql_categories);
    if ($result_categories) {
        while ($row = mysqli_fetch_assoc($result_categories)) {
            if (!empty($row['category'])) {
                $categories[] = $row['category'];
            }
        }
    }
} else {
    // Use venuetype as a fallback if category doesn't exist
    $sql_categories = "SELECT DISTINCT venuetype FROM venues WHERE venuetype IS NOT NULL ORDER BY venuetype";
    $result_categories = mysqli_query($conn, $sql_categories);
    if ($result_categories) {
        while ($row = mysqli_fetch_assoc($result_categories)) {
            if (!empty($row['venuetype'])) {
                $categories[] = $row['venuetype'];
            }
        }
    }
}

// Check if the building column exists
$check_building_sql = "SHOW COLUMNS FROM venues LIKE 'building'";
$building_exists = mysqli_query($conn, $check_building_sql);

// Get buildings for filter if the column exists
$buildings = [];
if (mysqli_num_rows($building_exists) > 0) {
    $sql_buildings = "SELECT DISTINCT building FROM venues ORDER BY building";
    $result_buildings = mysqli_query($conn, $sql_buildings);
    if ($result_buildings) {
        while ($row = mysqli_fetch_assoc($result_buildings)) {
            if (!empty($row['building'])) {
                $buildings[] = $row['building'];
            }
        }
    }
} else {
    // Use location as a fallback if building doesn't exist
    $sql_buildings = "SELECT DISTINCT location FROM venues WHERE location IS NOT NULL ORDER BY location";
    $result_buildings = mysqli_query($conn, $sql_buildings);
    if ($result_buildings) {
        while ($row = mysqli_fetch_assoc($result_buildings)) {
            if (!empty($row['location'])) {
                $buildings[] = $row['location'];
            }
        }
    }
}

// Apply filters if submitted
$filter_condition = "";
if (isset($_POST['apply_filters'])) {
    $category = isset($_POST['category']) ? mysqli_real_escape_string($conn, $_POST['category']) : "";
    $building = isset($_POST['building']) ? mysqli_real_escape_string($conn, $_POST['building']) : "";
    
    if (!empty($category)) {
        // Check which column to use for filtering
        if (mysqli_num_rows($category_exists) > 0) {
            $filter_condition .= " AND category = '$category'";
        } else {
            $filter_condition .= " AND venuetype = '$category'";
        }
    }
    
    if (!empty($building)) {
        // Check which column to use for filtering
        if (mysqli_num_rows($building_exists) > 0) {
            $filter_condition .= " AND building = '$building'";
        } else {
            $filter_condition .= " AND location = '$building'";
        }
    }
}

// Get free venues with search and filter conditions
$sql = "SELECT * FROM venues WHERE status = 'free' $search_condition $filter_condition ORDER BY venuename";
$result = mysqli_query($conn, $sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Free Venues | Student Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 15px;
        }
        
        h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 24px;
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .search-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .search-form {
            display: flex;
            gap: 10px;
        }
        
        .search-form input[type="text"] {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .search-form button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .search-form button:hover {
            background: var(--primary-dark);
        }
        
        .filter-container {
            margin-top: 15px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .filter-container label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-dark);
            font-weight: 500;
        }
        
        .filter-container select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .venue-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .venue-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .venue-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .venue-header {
            background: var(--primary-color);
            color: white;
            padding: 15px;
            font-weight: 500;
            font-size: 18px;
        }
        
        .venue-body {
            padding: 15px;
        }
        
        .venue-info {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-dark);
        }
        
        .venue-info i {
            color: var(--primary-color);
            width: 16px;
        }
        
        .venue-footer {
            padding: 15px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .venue-status {
            background: var(--success-color);
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .venue-action {
            display: inline-block;
            padding: 8px 15px;
            background: var(--primary-color);
            color: white;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .venue-action:hover {
            background: var(--primary-dark);
        }
        
        .no-venues {
            background: white;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .no-venues i {
            font-size: 48px;
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .no-venues h3 {
            margin: 0 0 10px 0;
            color: var(--text-dark);
        }
        
        .no-venues p {
            color: #777;
            margin: 0;
        }
        
        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
            }
            
            .filter-container {
                grid-template-columns: 1fr;
            }
            
            .venue-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 0 10px;
                margin: 20px auto;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/studentsidebar.php'); ?>

<div class="container">
    <h2><i class="fas fa-building"></i> Available Free Venues</h2>
    
    <div class="search-container">
        <form method="post" class="search-form">
            <input type="text" name="search_term" placeholder="Search venues..." value="<?php echo htmlspecialchars($search_term); ?>">
            <button type="submit" name="search"><i class="fas fa-search"></i> Search</button>
            <?php if (!empty($search_term)): ?>
                <button type="submit" name="clear_search"><i class="fas fa-times"></i> Clear</button>
            <?php endif; ?>
        </form>
        
        <form method="post" class="filter-container">
            <div>
                <label><?php echo mysqli_num_rows($category_exists) > 0 ? 'Category:' : 'Venue Type:'; ?></label>
                <select name="category">
                    <option value="">All <?php echo mysqli_num_rows($category_exists) > 0 ? 'Categories' : 'Types'; ?></option>
                    <?php foreach ($categories as $cat): ?>
                        <option value="<?php echo htmlspecialchars($cat); ?>" <?php echo isset($_POST['category']) && $_POST['category'] === $cat ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($cat); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label><?php echo mysqli_num_rows($building_exists) > 0 ? 'Building:' : 'Location:'; ?></label>
                <select name="building">
                    <option value="">All <?php echo mysqli_num_rows($building_exists) > 0 ? 'Buildings' : 'Locations'; ?></option>
                    <?php foreach ($buildings as $building): ?>
                        <option value="<?php echo htmlspecialchars($building); ?>" <?php echo isset($_POST['building']) && $_POST['building'] === $building ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($building); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <button type="submit" name="apply_filters"><i class="fas fa-filter"></i> Apply Filters</button>
            </div>
        </form>
    </div>
    
    <?php if (mysqli_num_rows($result) > 0): ?>
        <div class="venue-grid">
            <?php while ($row = mysqli_fetch_assoc($result)): ?>
                <div class="venue-card">
                    <div class="venue-header">
                        <?php echo htmlspecialchars($row['venuename']); ?>
                    </div>
                    <div class="venue-body">
                        <?php if (!empty($row['venuedescription'])): ?>
                            <div class="venue-info">
                                <i class="fas fa-info-circle"></i>
                                <?php echo htmlspecialchars($row['venuedescription']); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($row['building'])): ?>
                            <div class="venue-info">
                                <i class="fas fa-building"></i>
                                <?php echo htmlspecialchars($row['building']); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($row['floor'])): ?>
                            <div class="venue-info">
                                <i class="fas fa-layer-group"></i>
                                Floor: <?php echo htmlspecialchars($row['floor']); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($row['capacity'])): ?>
                            <div class="venue-info">
                                <i class="fas fa-users"></i>
                                Capacity: <?php echo htmlspecialchars($row['capacity']); ?> people
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($row['category'])): ?>
                            <div class="venue-info">
                                <i class="fas fa-tag"></i>
                                <?php echo htmlspecialchars($row['category']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="venue-footer">
                        <span class="venue-status">Available</span>
                        <a href="report_issue.php?venue=<?php echo $row['venueid']; ?>" class="venue-action">Report Issue</a>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>
    <?php else: ?>
        <div class="no-venues">
            <i class="fas fa-search"></i>
            <h3>No Free Venues Found</h3>
            <p>Try adjusting your search criteria or check back later.</p>
        </div>
    <?php endif; ?>
</div>

<script>
    // Add active class to current page link
    document.addEventListener('DOMContentLoaded', function() {
        const currentPage = window.location.pathname.split('/').pop();
        const menuLinks = document.querySelectorAll('#menu a');
        
        menuLinks.forEach(link => {
            const linkPage = link.getAttribute('href');
            if (linkPage === currentPage) {
                link.classList.add('active');
            }
        });
    });
</script>

<?php include('CSS/footer.php'); ?>
</body>
</html>
