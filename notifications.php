<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Get user details
$user_id = $_SESSION["user"];

// Get notifications for the user
$notifications_query = "SELECT * FROM notifications 
                        WHERE user_id = ? OR user_id IS NULL 
                        ORDER BY created_at DESC 
                        LIMIT 50";
$notifications_stmt = mysqli_prepare($conn, $notifications_query);
mysqli_stmt_bind_param($notifications_stmt, "i", $user_id);
mysqli_stmt_execute($notifications_stmt);
$notifications_result = mysqli_stmt_get_result($notifications_stmt);

// Mark all unread notifications as read
$mark_read_query = "UPDATE notifications SET is_read = 1 WHERE user_id = ? AND is_read = 0";
$mark_read_stmt = mysqli_prepare($conn, $mark_read_query);
mysqli_stmt_bind_param($mark_read_stmt, "i", $user_id);
mysqli_stmt_execute($mark_read_stmt);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Notifications | Student Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 15px;
        }
        
        h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 24px;
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .notifications-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .notification-filters {
            display: flex;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            gap: 15px;
        }
        
        .filter-btn {
            background: none;
            border: none;
            padding: 5px 10px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            color: var(--text-dark);
            transition: all 0.3s;
        }
        
        .filter-btn.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .notification-list {
            max-height: 600px;
            overflow-y: auto;
        }
        
        .notification-item {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            gap: 15px;
            transition: background-color 0.3s;
        }
        
        .notification-item:hover {
            background-color: rgba(41, 128, 185, 0.05);
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }
        
        .notification-icon.info {
            background-color: var(--primary-light);
        }
        
        .notification-icon.success {
            background-color: var(--success-color);
        }
        
        .notification-icon.warning {
            background-color: var(--warning-color);
        }
        
        .notification-icon.danger {
            background-color: var(--danger-color);
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-title {
            font-weight: 500;
            margin: 0 0 5px 0;
            color: var(--text-dark);
        }
        
        .notification-message {
            margin: 0 0 10px 0;
            color: #666;
            line-height: 1.4;
        }
        
        .notification-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #999;
        }
        
        .notification-time {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .notification-actions a {
            color: var(--primary-color);
            text-decoration: none;
            margin-left: 10px;
        }
        
        .notification-actions a:hover {
            text-decoration: underline;
        }
        
        .notification-unread {
            position: relative;
        }
        
        .notification-unread::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 10px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--primary-color);
        }
        
        .empty-state {
            text-align: center;
            padding: 50px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 40px;
            color: #ddd;
            margin-bottom: 10px;
        }
        
        .empty-state p {
            margin: 0;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
                margin: 20px auto;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/studentsidebar.php'); ?>

<div class="container">
    <h2><i class="fas fa-bell"></i> Notifications</h2>
    
    <div class="notifications-container">
        <div class="notification-filters">
            <button class="filter-btn active" data-filter="all">All</button>
            <button class="filter-btn" data-filter="info">Information</button>
            <button class="filter-btn" data-filter="success">Success</button>
            <button class="filter-btn" data-filter="warning">Warnings</button>
            <button class="filter-btn" data-filter="danger">Alerts</button>
        </div>
        
        <div class="notification-list">
            <?php if (mysqli_num_rows($notifications_result) > 0): ?>
                <?php while ($notification = mysqli_fetch_assoc($notifications_result)): ?>
                    <div class="notification-item <?php echo $notification['is_read'] ? '' : 'notification-unread'; ?>" data-type="<?php echo htmlspecialchars($notification['type']); ?>">
                        <div class="notification-icon <?php echo htmlspecialchars($notification['type']); ?>">
                            <?php if ($notification['type'] == 'info'): ?>
                                <i class="fas fa-info"></i>
                            <?php elseif ($notification['type'] == 'success'): ?>
                                <i class="fas fa-check"></i>
                            <?php elseif ($notification['type'] == 'warning'): ?>
                                <i class="fas fa-exclamation"></i>
                            <?php elseif ($notification['type'] == 'danger'): ?>
                                <i class="fas fa-exclamation-triangle"></i>
                            <?php endif; ?>
                        </div>
                        <div class="notification-content">
                            <h4 class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></h4>
                            <p class="notification-message"><?php echo nl2br(htmlspecialchars($notification['message'])); ?></p>
                            <div class="notification-meta">
                                <div class="notification-time">
                                    <i class="far fa-clock"></i>
                                    <?php 
                                        $timestamp = strtotime($notification['created_at']);
                                        $now = time();
                                        $diff = $now - $timestamp;
                                        
                                        if ($diff < 60) {
                                            echo "Just now";
                                        } elseif ($diff < 3600) {
                                            echo floor($diff / 60) . " minutes ago";
                                        } elseif ($diff < 86400) {
                                            echo floor($diff / 3600) . " hours ago";
                                        } elseif ($diff < 604800) {
                                            echo floor($diff / 86400) . " days ago";
                                        } else {
                                            echo date('M d, Y', $timestamp);
                                        }
                                    ?>
                                </div>
                                <div class="notification-actions">
                                    <?php if (!empty($notification['action_url'])): ?>
                                        <a href="<?php echo htmlspecialchars($notification['action_url']); ?>">
                                            <?php echo htmlspecialchars($notification['action_text'] ?: 'View'); ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            <?php else: ?>
                <div class="empty-state">
                    <i class="far fa-bell-slash"></i>
                    <p>No notifications found</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add active class to current page link
        const currentPage = window.location.pathname.split('/').pop();
        const menuLinks = document.querySelectorAll('#menu a');
        
        menuLinks.forEach(link => {
            const linkPage = link.getAttribute('href');
            if (linkPage === currentPage) {
                link.classList.add('active');
            }
        });
        
        // Notification filtering
        const filterButtons = document.querySelectorAll('.filter-btn');
        const notificationItems = document.querySelectorAll('.notification-item');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                filterButtons.forEach(btn => btn.classList.remove('active'));
                
                // Add active class to clicked button
                this.classList.add('active');
                
                const filterValue = this.dataset.filter;
                
                // Filter notifications
                notificationItems.forEach(item => {
                    if (filterValue === 'all' || item.dataset.type === filterValue) {
                        item.style.display = 'flex';
                    } else {
                        item.style.display = 'none';
                    }
                });
                
                // Show empty state if no notifications match filter
                const visibleCount = Array.from(notificationItems).filter(item => 
                    item.style.display !== 'none'
                ).length;
                
                let emptyState = document.querySelector('.empty-state');
                
                if (visibleCount === 0) {
                    if (!emptyState) {
                        emptyState = document.createElement('div');
                        emptyState.className = 'empty-state';
                        emptyState.innerHTML = `
                            <i class="far fa-bell-slash"></i>
                            <p>No ${filterValue === 'all' ? '' : filterValue} notifications found</p>
                        `;
                        document.querySelector('.notification-list').appendChild(emptyState);
                    } else {
                        emptyState.innerHTML = `
                            <i class="far fa-bell-slash"></i>
                            <p>No ${filterValue === 'all' ? '' : filterValue} notifications found</p>
                        `;
                        emptyState.style.display = 'block';
                    }
                } else if (emptyState) {
                    emptyState.style.display = 'none';
                }
            });
        });
    });
</script>

</body>
</html>

