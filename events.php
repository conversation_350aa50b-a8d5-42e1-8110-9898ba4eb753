<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Get user details
$user_id = $_SESSION["user"];

// Get user's course, year, and stream
$user_query = "SELECT course, year, stream FROM users WHERE user_id = ?";
$user_stmt = mysqli_prepare($conn, $user_query);
mysqli_stmt_bind_param($user_stmt, "i", $user_id);
mysqli_stmt_execute($user_stmt);
$user_result = mysqli_stmt_get_result($user_stmt);
$user = mysqli_fetch_assoc($user_result);

// Get all upcoming events, including those specific to the user's course/year/stream
$events_query = "SELECT e.*, v.venuename, v.location 
                FROM events e 
                LEFT JOIN venues v ON e.venue_id = v.venueid 
                WHERE e.event_date >= CURDATE() 
                AND (e.course = ? OR e.course = 'All' OR e.year = ? OR e.year = 'All' OR e.stream = ? OR e.stream = 'All')
                ORDER BY e.event_date ASC, e.start_time ASC";
$events_stmt = mysqli_prepare($conn, $events_query);
mysqli_stmt_bind_param($events_stmt, "sis", $user['course'], $user['year'], $user['stream']);
mysqli_stmt_execute($events_stmt);
$events_result = mysqli_stmt_get_result($events_stmt);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Events | Student Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 15px;
        }
        
        h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 24px;
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .filter-container {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .filter-label {
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .search-box {
            flex-grow: 1;
            position: relative;
        }
        
        .search-box input {
            width: 100%;
            padding: 10px 15px 10px 40px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }
        
        .events-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .event-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .event-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .event-header {
            padding: 20px;
            background: var(--primary-color);
            color: white;
            position: relative;
        }
        
        .event-date {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .event-title {
            font-size: 18px;
            font-weight: 500;
            margin: 0 0 5px 0;
        }
        
        .event-organizer {
            font-size: 14px;
            opacity: 0.9;
            margin: 0;
        }
        
        .event-body {
            padding: 20px;
        }
        
        .event-info {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: var(--text-dark);
        }
        
        .event-info i {
            color: var(--primary-color);
            width: 20px;
        }
        
        .event-description {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            color: var(--text-dark);
            line-height: 1.5;
        }
        
        .event-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
            text-decoration: none;
        }
        
        .btn:hover {
            background-color: var(--primary-dark);
        }
        
        .btn-outline {
            background-color: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-outline:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 50px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 40px;
            color: #ddd;
            margin-bottom: 10px;
        }
        
        .empty-state p {
            margin: 0;
        }
        
        .event-tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        
        .tag-course {
            background-color: rgba(41, 128, 185, 0.2);
            color: #2980b9;
        }
        
        .tag-year {
            background-color: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }
        
        .tag-stream {
            background-color: rgba(243, 156, 18, 0.2);
            color: #d35400;
        }
        
        .tag-all {
            background-color: rgba(155, 89, 182, 0.2);
            color: #9b59b6;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
                margin: 20px auto;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/studentsidebar.php'); ?>

<div class="container">
    <h2><i class="fas fa-calendar-alt"></i> Upcoming Events</h2>
    
    <div class="filter-container">
        <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" id="eventSearch" placeholder="Search events...">
        </div>
        
        <div class="filter-group">
            <span class="filter-label">Date:</span>
            <select id="dateFilter" class="filter-select">
                <option value="all">All</option>
                <option value="today">Today</option>
                <option value="tomorrow">Tomorrow</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
            </select>
        </div>
        
        <div class="filter-group">
            <span class="filter-label">Type:</span>
            <select id="typeFilter" class="filter-select">
                <option value="all">All</option>
                <option value="course">My Course</option>
                <option value="year">My Year</option>
                <option value="stream">My Stream</option>
                <option value="general">General</option>
            </select>
        </div>
    </div>
    
    <div class="events-list" id="eventsList">
        <?php if (mysqli_num_rows($events_result) > 0): ?>
            <?php while ($event = mysqli_fetch_assoc($events_result)): ?>
                <div class="event-card" 
                     data-date="<?php echo date('Y-m-d', strtotime($event['event_date'])); ?>"
                     data-course="<?php echo htmlspecialchars($event['course']); ?>"
                     data-year="<?php echo htmlspecialchars($event['year']); ?>"
                     data-stream="<?php echo htmlspecialchars($event['stream']); ?>">
                    <div class="event-header">
                        <span class="event-date">
                            <?php echo date('D, M d, Y', strtotime($event['event_date'])); ?>
                        </span>
                        <h3 class="event-title"><?php echo htmlspecialchars($event['event_title']); ?></h3>
                        <p class="event-organizer">Organized by: <?php echo htmlspecialchars($event['organizer']); ?></p>
                    </div>
                    <div class="event-body">
                        <div class="event-info">
                            <i class="fas fa-clock"></i>
                            <span>
                                <?php 
                                    echo date('g:i A', strtotime($event['start_time'])); 
                                    echo ' - '; 
                                    echo date('g:i A', strtotime($event['end_time']));
                                ?>
                            </span>
                        </div>
                        
                        <?php if (!empty($event['venuename'])): ?>
                            <div class="event-info">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>
                                    <?php echo htmlspecialchars($event['venuename']); ?>
                                    <?php if (!empty($event['location'])): ?>
                                        (<?php echo htmlspecialchars($event['location']); ?>)
                                    <?php endif; ?>
                                </span>
                            </div>
                        <?php endif; ?>
                        
                        <div class="event-info">
                            <i class="fas fa-users"></i>
                            <span>
                                <?php if ($event['course'] == 'All' && $event['year'] == 'All' && $event['stream'] == 'All'): ?>
                                    <span class="event-tag tag-all">All Students</span>
                                <?php else: ?>
                                    <?php if ($event['course'] != 'All'): ?>
                                        <span class="event-tag tag-course"><?php echo htmlspecialchars($event['course']); ?></span>
                                    <?php endif; ?>
                                    <?php if ($event['year'] != 'All'): ?>
                                        <span class="event-tag tag-year">Year <?php echo htmlspecialchars($event['year']); ?></span>
                                    <?php endif; ?>
                                    <?php if ($event['stream'] != 'All'): ?>
                                        <span class="event-tag tag-stream">Stream <?php echo htmlspecialchars($event['stream']); ?></span>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </span>
                        </div>
                        
                        <?php if (!empty($event['event_description'])): ?>
                            <div class="event-description">
                                <?php echo nl2br(htmlspecialchars($event['event_description'])); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($event['venue_id'])): ?>
                            <div class="event-actions">
                                <a href="venues.php" class="btn">View Venue</a>
                                <?php if (!empty($event['event_link'])): ?>
                                    <a href="<?php echo htmlspecialchars($event['event_link']); ?>" class="btn btn-outline" target="_blank">More Info</a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endwhile; ?>
        <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-calendar-times"></i>
                <h3>No Upcoming Events</h3>
                <p>There are no upcoming events scheduled for you at this time.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add active class to current page link
        const currentPage = window.location.pathname.split('/').pop();
        const menuLinks = document.querySelectorAll('#menu a');
        
        menuLinks.forEach(link => {
            const linkPage = link.getAttribute('href');
            if (linkPage === currentPage) {
                link.classList.add('active');
            }
        });
        
        // Event filtering functionality
        const eventSearch = document.getElementById('eventSearch');
        const dateFilter = document.getElementById('dateFilter');
        const typeFilter = document.getElementById('typeFilter');
        const eventsList = document.getElementById('eventsList');
        const eventCards = document.querySelectorAll('.event-card');
        
        function filterEvents() {
            const searchTerm = eventSearch.value.toLowerCase();
            const dateValue = dateFilter.value;
            const typeValue = typeFilter.value;
            
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            
            const nextWeek = new Date(today);
            nextWeek.setDate(nextWeek.getDate() + 7);
            
            const nextMonth = new Date(today);
            nextMonth.setMonth(nextMonth.getMonth() + 1);
            
            let visibleCount = 0;
            
            eventCards.forEach(card => {
                const eventTitle = card.querySelector('.event-title').textContent.toLowerCase();
                const eventOrganizer = card.querySelector('.event-organizer').textContent.toLowerCase();
                const eventDescription = card.querySelector('.event-description') ? 
                                        card.querySelector('.event-description').textContent.toLowerCase() : '';
                
                const eventDateStr = card.dataset.date;
                const eventDate = new Date(eventDateStr);
                
                const course = card.dataset.course;
                const year = card.dataset.year;
                const stream = card.dataset.stream;
                
                // Search term matching
                const matchesSearch = eventTitle.includes(searchTerm) || 
                                     eventOrganizer.includes(searchTerm) || 
                                     eventDescription.includes(searchTerm);
                
                // Date filtering
                let matchesDate = true;
                if (dateValue === 'today') {
                    matchesDate = eventDate.getTime() === today.getTime();
                } else if (dateValue === 'tomorrow') {
                    matchesDate = eventDate.getTime() === tomorrow.getTime();
                } else if (dateValue === 'week') {
                    matchesDate = eventDate >= today && eventDate < nextWeek;
                } else if (dateValue === 'month') {
                    matchesDate = eventDate >= today && eventDate < nextMonth;
                }
                
                // Type filtering
                let matchesType = true;
                if (typeValue === 'course') {
                    matchesType = course === '<?php echo $user['course']; ?>';
                } else if (typeValue === 'year') {
                    matchesType = year === '<?php echo $user['year']; ?>';
                } else if (typeValue === 'stream') {
                    matchesType = stream === '<?php echo $user['stream']; ?>';
                } else if (typeValue === 'general') {
                    matchesType = course === 'All' || year === 'All' || stream === 'All';
                }
                
                if (matchesSearch && matchesDate && matchesType) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });
            
            // Show empty state if no events match filters
            const emptyState = document.querySelector('.empty-state');
            if (emptyState) {
                if (visibleCount === 0 && eventCards.length > 0) {
                    // Create empty state if it doesn't exist
                    if (!document.querySelector('.filtered-empty-state')) {
                        const filteredEmptyState = document.createElement('div');
                        filteredEmptyState.className = 'empty-state filtered-empty-state';
                        filteredEmptyState.innerHTML = `
                            <i class="fas fa-filter"></i>
                            <p>No events match your filters</p>
                        `;
                        eventsList.appendChild(filteredEmptyState);
                    } else {
                        document.querySelector('.filtered-empty-state').style.display = 'block';
                    }
                } else if (document.querySelector('.filtered-empty-state')) {
                    document.querySelector('.filtered-empty-state').style.display = 'none';
                }
            }
        }
        
        eventSearch.addEventListener('input', filterEvents);
        dateFilter.addEventListener('change', filterEvents);
        typeFilter.addEventListener('change', filterEvents);
    });
</script>

</body>
</html>





