<?php
session_start();
require_once "database.php";

// Check if user is admin
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    header("Location: login.php");
    exit();
}

$success_message = "";
$error_message = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $full_name = trim($_POST["full_name"]);
    $email = trim($_POST["email"]);
    $password = $_POST["password"];
    $role = $_POST["role"];
    $course = $_POST["course"] ?? '';
    $year = $_POST["year"] ?? '';
    $stream = $_POST["stream"] ?? '';
    
    // Validate inputs
    if (empty($full_name) || empty($email) || empty($password) || empty($role)) {
        $error_message = "Required fields cannot be empty";
    } else {
        // Check if email already exists
        $check_email = mysqli_prepare($conn, "SELECT user_id FROM users WHERE email = ?");
        mysqli_stmt_bind_param($check_email, "s", $email);
        mysqli_stmt_execute($check_email);
        mysqli_stmt_store_result($check_email);
        
        if (mysqli_stmt_num_rows($check_email) > 0) {
            $error_message = "Email already exists";
        } else {
            // Hash password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert user
            $insert_sql = "INSERT INTO users (full_name, email, password, role, course, year, stream) VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = mysqli_prepare($conn, $insert_sql);
            mysqli_stmt_bind_param($stmt, "sssssss", $full_name, $email, $hashed_password, $role, $course, $year, $stream);
            
            if (mysqli_stmt_execute($stmt)) {
                $success_message = "User added successfully";
                // Clear form data
                $full_name = $email = $password = $role = $course = $year = $stream = "";
            } else {
                $error_message = "Error: " . mysqli_error($conn);
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add User</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input, select { width: 100%; padding: 8px; box-sizing: border-box; }
        .btn { background: #4CAF50; color: white; padding: 10px 15px; border: none; cursor: pointer; }
        .error { color: red; }
        .success { color: green; }
        .student-fields { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <h2>Add New User</h2>
        
        <?php if (!empty($error_message)): ?>
            <div class="error"><?php echo $error_message; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success_message)): ?>
            <div class="success"><?php echo $success_message; ?></div>
        <?php endif; ?>
        
        <form method="post" action="">
            <div class="form-group">
                <label for="full_name">Full Name:</label>
                <input type="text" id="full_name" name="full_name" value="<?php echo isset($full_name) ? $full_name : ''; ?>" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" value="<?php echo isset($email) ? $email : ''; ?>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label for="role">Role:</label>
                <select id="role" name="role" required>
                    <option value="">Select Role</option>
                    <option value="admin">Admin</option>
                    <option value="lecturer">Lecturer</option>
                    <option value="student">Student</option>
                </select>
            </div>
            
            <div id="student-fields" class="student-fields">
                <div class="form-group">
                    <label for="course">Course:</label>
                    <select id="course" name="course">
                        <option value="">Select Course</option>
                        <option value="BSc IT">BSc IT</option>
                        <option value="ODIT">ODIT</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="year">Year:</label>
                    <select id="year" name="year">
                        <option value="">Select Year</option>
                        <option value="1">Year 1</option>
                        <option value="2">Year 2</option>
                        <option value="3">Year 3</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="stream">Stream:</label>
                    <select id="stream" name="stream">
                        <option value="">Select Stream</option>
                        <option value="A">A</option>
                        <option value="B">B</option>
                        <option value="C">C</option>
                        <option value="SysDev">SysDev</option>
                        <option value="SysAdmin">SysAdmin</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn">Add User</button>
            </div>
        </form>
        
        <p><a href="index.php">Back to Dashboard</a></p>
    </div>
    
    <script>
        // Show/hide student fields based on role selection
        document.getElementById('role').addEventListener('change', function() {
            var studentFields = document.getElementById('student-fields');
            if (this.value === 'student') {
                studentFields.style.display = 'block';
            } else {
                studentFields.style.display = 'none';
            }
        });
    </script>
</body>
</html>