<?php
session_start();

// Check if the user is logged in and is an admin
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    // Redirect unauthorized users to login page
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Initialize variables
$success_message = $error_message = "";

// Function to backup the database
function backupDatabase($host, $user, $password, $database) {
    $date = date("Y-m-d-H-i-s");
    $backupFile = $database . '_' . $date . '.sql';
    $backupPath = 'backups/' . $backupFile;
    
    // Create backups directory if it doesn't exist
    if (!file_exists('backups')) {
        mkdir('backups', 0777, true);
    }
    
    // Command for mysqldump
    $command = "mysqldump --opt --host=$host --user=$user";
    if (!empty($password)) {
        $command .= " --password=$password";
    }
    $command .= " $database > $backupPath";
    
    // Execute the command
    exec($command, $output, $return_var);
    
    if ($return_var === 0) {
        // Backup successful
        return [
            'success' => true,
            'file' => $backupFile,
            'path' => $backupPath,
            'size' => filesize($backupPath)
        ];
    } else {
        // Backup failed
        return [
            'success' => false,
            'error' => 'Database backup failed. Error code: ' . $return_var
        ];
    }
}

// Process backup request
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['backup'])) {
    $result = backupDatabase($hostName, $dbUser, $dbPassword, $dbName);
    
    if ($result['success']) {
        $success_message = "Database backup created successfully: " . $result['file'] . " (" . round($result['size'] / 1024 / 1024, 2) . " MB)";
        
        // Log the backup action
        $log_sql = "INSERT INTO system_logs (user_id, action, details, ip_address) VALUES (?, 'Database Backup', ?, ?)";
        $log_stmt = mysqli_prepare($conn, $log_sql);
        $log_details = "Created backup: " . $result['file'];
        $ip_address = $_SERVER['REMOTE_ADDR'];
        mysqli_stmt_bind_param($log_stmt, "iss", $_SESSION["user"], $log_details, $ip_address);
        mysqli_stmt_execute($log_stmt);
    } else {
        $error_message = $result['error'];
    }
}

// Get list of existing backups
$backups = [];
if (file_exists('backups')) {
    $files = scandir('backups');
    foreach ($files as $file) {
        if ($file != '.' && $file != '..' && pathinfo($file, PATHINFO_EXTENSION) == 'sql') {
            $backups[] = [
                'file' => $file,
                'path' => 'backups/' . $file,
                'size' => filesize('backups/' . $file),
                'date' => filemtime('backups/' . $file)
            ];
        }
    }
    
    // Sort backups by date (newest first)
    usort($backups, function($a, $b) {
        return $b['date'] - $a['date'];
    });
}

// Handle backup deletion
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $file_to_delete = 'backups/' . basename($_GET['delete']);
    
    if (file_exists($file_to_delete) && unlink($file_to_delete)) {
        $success_message = "Backup file deleted successfully.";
        
        // Log the deletion action
        $log_sql = "INSERT INTO system_logs (user_id, action, details, ip_address) VALUES (?, 'Delete Backup', ?, ?)";
        $log_stmt = mysqli_prepare($conn, $log_sql);
        $log_details = "Deleted backup: " . basename($_GET['delete']);
        $ip_address = $_SERVER['REMOTE_ADDR'];
        mysqli_stmt_bind_param($log_stmt, "iss", $_SESSION["user"], $log_details, $ip_address);
        mysqli_stmt_execute($log_stmt);
        
        // Redirect to remove the 'delete' parameter from URL
        header("Location: backup_database.php");
        exit();
    } else {
        $error_message = "Failed to delete backup file.";
    }
}

// Handle backup download
if (isset($_GET['download']) && !empty($_GET['download'])) {
    $file_to_download = 'backups/' . basename($_GET['download']);
    
    if (file_exists($file_to_download)) {
        // Log the download action
        $log_sql = "INSERT INTO system_logs (user_id, action, details, ip_address) VALUES (?, 'Download Backup', ?, ?)";
        $log_stmt = mysqli_prepare($conn, $log_sql);
        $log_details = "Downloaded backup: " . basename($_GET['download']);
        $ip_address = $_SERVER['REMOTE_ADDR'];
        mysqli_stmt_bind_param($log_stmt, "iss", $_SESSION["user"], $log_details, $ip_address);
        mysqli_stmt_execute($log_stmt);
        
        // Set headers for download
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file_to_download) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file_to_download));
        readfile($file_to_download);
        exit;
    } else {
        $error_message = "Backup file not found.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Database Backup | Admin Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-dark);
        }
        
        .container {
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            margin: 20px;
            transition: margin-left 0.3s ease;
        }
        
        #menuToggle:checked ~ .container {
            margin-left: 270px;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .page-header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .card-header {
            background: var(--primary-light);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 500;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            margin-right: 10px;
        }
        
        .btn i {
            margin-right: 5px;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-danger {
            background: var(--danger-color);
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-success {
            background: var(--success-color);
            color: white;
        }
        
        .btn-success:hover {
            background: #27ae60;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            border: 1px solid var(--success-color);
            color: #27ae60;
        }
        
        .alert-danger {
            background-color: rgba(231, 76, 60, 0.2);
            border: 1px solid var(--danger-color);
            color: #c0392b;
        }
        
        .backup-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .backup-table th,
        .backup-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .backup-table th {
            background-color: #f8f9fa;
            font-weight: 500;
        }
        
        .backup-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .backup-table .actions {
            display: flex;
            gap: 10px;
        }
        
        .backup-table .actions a {
            color: var(--primary-color);
            text-decoration: none;
            transition: all 0.2s;
        }
        
        .backup-table .actions a:hover {
            color: var(--primary-dark);
        }
        
        .backup-table .actions a.delete {
            color: var(--danger-color);
        }
        
        .backup-table .actions a.delete:hover {
            color: #c0392b;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #777;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #ddd;
        }
        
        .empty-state p {
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }
            
            .page-header {
                padding: 20px;
            }
            
            .page-header h1 {
                font-size: 24px;
            }
            
            .backup-table th,
            .backup-table td {
                padding: 8px 10px;
            }
            
            .backup-table .actions {
                flex-direction: column;
                gap: 5px;
            }
            
            #menuToggle:checked ~ .container {
                margin-left: 20px;
                opacity: 0.4;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/sidebar.php'); ?>

<div class="container">
    <div class="page-header">
        <h1>Database Backup</h1>
        <p>Create and manage backups of your venue management system database.</p>
    </div>
    
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php