<?php
session_start();

// Check if the user is logged in and is an admin
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    // Redirect unauthorized users to login page
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Fetch total number of users
$sqlUsers = "SELECT COUNT(*) AS total_users FROM users";
$resultUsers = mysqli_query($conn, $sqlUsers);
$rowUsers = mysqli_fetch_assoc($resultUsers);
$totalUsers = $rowUsers['total_users'];

// Fetch total number of venues
$sqlVenues = "SELECT COUNT(*) AS total_venues FROM venues";
$resultVenues = mysqli_query($conn, $sqlVenues);
$rowVenues = mysqli_fetch_assoc($resultVenues);
$totalVenues = $rowVenues['total_venues'];

// Fetch total number of free venues
$sqlFreeVenues = "SELECT COUNT(*) AS total_free_venues FROM venues WHERE status = 'free'";
$resultFreeVenues = mysqli_query($conn, $sqlFreeVenues);
$rowFreeVenues = mysqli_fetch_assoc($resultFreeVenues);
$totalFreeVenues = $rowFreeVenues['total_free_venues'];

// Fetch recent issues reported
$sqlIssues = "SELECT i.*, v.venuename FROM issues i 
              JOIN venues v ON i.venue_id = v.venueid 
              ORDER BY i.reported_date DESC LIMIT 5";
$resultIssues = mysqli_query($conn, $sqlIssues);

// Fetch recent user activities
$sqlActivities = "SELECT u.full_name, u.role, l.login_time 
                 FROM login_logs l 
                 JOIN users u ON l.user_id = u.user_id 
                 ORDER BY l.login_time DESC LIMIT 5";
$resultActivities = mysqli_query($conn, $sqlActivities);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Admin Dashboard | Venue Management System</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-dark);
        }
        
        .dashboard-container {
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            margin: 20px;
            transition: margin-left 0.3s ease;
        }
        
        #menuToggle:checked ~ .dashboard-container {
            margin-left: 270px;
        }
        
        .welcome-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .welcome-header::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 150px;
            height: 100%;
            background: url('images/pattern.svg') no-repeat right center;
            opacity: 0.1;
        }
        
        .welcome-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .welcome-header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            padding: 25px;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.15);
        }
        
        .stat-card h2 {
            font-size: 18px;
            margin-bottom: 15px;
            color: white;
            position: relative;
            z-index: 1;
        }
        
        .stat-card .count-number {
            font-size: 36px;
            font-weight: bold;
            color: white;
            position: relative;
            z-index: 1;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.1);
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 70%);
        }
        
        .users-card {
            background: linear-gradient(135deg, #33CAFF, #0080ff);
        }
        
        .venues-card {
            background: linear-gradient(135deg, #FF6B6B, #cc0000);
        }
        
        .free-venues-card {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }
        
        .quick-actions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            grid-column: 1 / -1;
            margin-bottom: 20px;
        }
        
        .quick-actions h3 {
            margin-bottom: 20px;
            color: var(--primary-color);
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
            font-size: 18px;
        }
        
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .action-btn {
            padding: 12px 20px;
            border-radius: 5px;
            background-color: var(--primary-color);
            color: white;
            text-decoration: none;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }
        
        .action-btn:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .action-btn i {
            font-size: 16px;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .card-header {
            background: var(--primary-light);
            color: white;
            padding: 15px 20px;
            font-size: 16px;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-header a {
            color: white;
            text-decoration: none;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            opacity: 0.8;
            transition: opacity 0.3s;
        }
        
        .card-header a:hover {
            opacity: 1;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .issue-list, .activity-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .issue-item, .activity-item {
            padding: 15px;
            border-radius: 6px;
            background: var(--bg-light);
            border-left: 4px solid var(--primary-color);
        }
        
        .issue-item {
            border-left-color: var(--warning-color);
        }
        
        .issue-title, .activity-title {
            font-weight: 500;
            margin-bottom: 5px;
            color: var(--text-dark);
        }
        
        .issue-details, .activity-details {
            font-size: 14px;
            color: #666;
            display: flex;
            justify-content: space-between;
        }
        
        .issue-venue, .activity-role {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .issue-date, .activity-date {
            font-size: 12px;
            color: #888;
        }
        
        .empty-state {
            padding: 30px;
            text-align: center;
            color: #888;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #ddd;
        }
        
        @media (max-width: 992px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-container {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            }
        }
        
        @media (max-width: 768px) {
            .dashboard-container {
                padding: 15px;
                margin: 10px;
            }
            
            .welcome-header {
                padding: 20px;
            }
            
            .welcome-header h1 {
                font-size: 24px;
            }
            
            .stats-container {
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }
            
            .stat-card {
                padding: 20px 15px;
            }
            
            .stat-card .count-number {
                font-size: 28px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .action-btn {
                width: 100%;
                justify-content: center;
            }
            
            #menuToggle:checked ~ .dashboard-container {
                margin-left: 20px;
                opacity: 0.4;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/sidebar.php'); ?>

<div class="dashboard-container">
    <div class="welcome-header">
        <h1>Welcome to Admin Dashboard</h1>
        <p>Manage your venues, users, and system settings from this central hub.</p>
    </div>
    
    <div class="stats-container">
        <div class="stat-card users-card">
            <h2>Total Users</h2>
            <p class="count-number"><?php echo $totalUsers; ?></p>
        </div>
        
        <div class="stat-card venues-card">
            <h2>Total Venues</h2>
            <p class="count-number"><?php echo $totalVenues; ?></p>
        </div>
        
        <div class="stat-card free-venues-card">
            <h2>Available Venues</h2>
            <p class="count-number"><?php echo $totalFreeVenues; ?></p>
        </div>
    </div>
    
    <div class="quick-actions">
        <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
        <div class="action-buttons">
            <a href="registration.php" class="action-btn">
                <i class="fas fa-user-plus"></i> Add New User
            </a>
            <a href="add_venue.php" class="action-btn">
                <i class="fas fa-map-marker-alt"></i> Add New Venue
            </a>
            <a href="upload_timetable.php" class="action-btn">
                <i class="fas fa-calendar-alt"></i> Upload Timetable
            </a>
            <a href="view_reports.php" class="action-btn">
                <i class="fas fa-clipboard-list"></i> View Reports
            </a>
            <a href="requests.php" class="action-btn">
                <i class="fas fa-key"></i> Password Requests
            </a>
        </div>
    </div>
    
    <div class="dashboard-grid">
        <div class="left-column">
            <div class="dashboard-card">
                <div class="card-header">
                    <span><i class="fas fa-exclamation-circle"></i> Recent Issues Reported</span>
                    <a href="view_reports.php"><i class="fas fa-external-link-alt"></i> View All</a>
                </div>
                <div class="card-body">
                    <?php if (mysqli_num_rows($resultIssues) > 0): ?>
                        <div class="issue-list">
                            <?php while ($issue = mysqli_fetch_assoc($resultIssues)): ?>
                                <div class="issue-item">
                                    <div class="issue-title"><?php echo htmlspecialchars($issue['issue_title']); ?></div>
                                    <div class="issue-details">
                                        <span class="issue-venue">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <?php echo htmlspecialchars($issue['venuename']); ?>
                                        </span>
                                        <span class="issue-date">
                                            <?php echo date('M d, Y', strtotime($issue['reported_date'])); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-check-circle"></i>
                            <p>No issues have been reported yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="right-column">
            <div class="dashboard-card">
                <div class="card-header">
                    <span><i class="fas fa-history"></i> Recent User Activities</span>
                </div>
                <div class="card-body">
                    <?php if (mysqli_num_rows($resultActivities) > 0): ?>
                        <div class="activity-list">
                            <?php while ($activity = mysqli_fetch_assoc($resultActivities)): ?>
                                <div class="activity-item">
                                    <div class="activity-title">
                                        <?php echo htmlspecialchars($activity['full_name'] ?? 'Unknown User'); ?>
                                    </div>
                                    <div class="activity-details">
                                        <span class="activity-role">
                                            <i class="fas fa-user-tag"></i>
                                            <?php echo ucfirst(htmlspecialchars($activity['role'] ?? 'Unknown')); ?>
                                        </span>
                                        <span class="activity-date">
                                            <?php echo date('M d, Y H:i', strtotime($activity['login_time'])); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-history"></i>
                            <p>No recent activities to display.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include('CSS/footer.php'); ?>

</body>
</html>
