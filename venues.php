<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Get user details
$user_id = $_SESSION["user"];

// Initialize variables
$success_message = "";
$error_message = "";

// Get all venues
$venues_query = "SELECT * FROM venues ORDER BY status, venuename";
$venues_result = mysqli_query($conn, $venues_query);

// Get user's active check-ins
$active_checkins_query = "SELECT venue_id FROM venue_checkins WHERE user_id = ? AND check_out_time IS NULL";
$stmt = mysqli_prepare($conn, $active_checkins_query);
mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);
$active_checkins_result = mysqli_stmt_get_result($stmt);

$active_venues = [];
while ($checkin = mysqli_fetch_assoc($active_checkins_result)) {
    $active_venues[] = $checkin['venue_id'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Venues | Student Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 15px;
        }
        
        h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 24px;
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .filter-container {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .filter-label {
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .search-box {
            flex-grow: 1;
            position: relative;
        }
        
        .search-box input {
            width: 100%;
            padding: 10px 15px 10px 40px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }
        
        .venues-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .venue-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .venue-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .venue-header {
            position: relative;
            padding: 20px;
            background: var(--primary-color);
            color: white;
        }
        
        .venue-status {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-available {
            background-color: rgba(46, 204, 113, 0.9);
            color: white;
        }
        
        .status-occupied {
            background-color: rgba(231, 76, 60, 0.9);
            color: white;
        }
        
        .status-maintenance {
            background-color: rgba(243, 156, 18, 0.9);
            color: white;
        }
        
        .venue-name {
            font-size: 18px;
            font-weight: 500;
            margin: 0 0 5px 0;
        }
        
        .venue-type {
            font-size: 14px;
            opacity: 0.9;
            margin: 0;
        }
        
        .venue-body {
            padding: 20px;
        }
        
        .venue-info {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: var(--text-dark);
        }
        
        .venue-info i {
            color: var(--primary-color);
            width: 20px;
        }
        
        .venue-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
            text-decoration: none;
            text-align: center;
        }
        
        .btn:hover {
            background-color: var(--primary-dark);
        }
        
        .btn-block {
            display: block;
            width: 100%;
        }
        
        .btn-success {
            background-color: var(--success-color);
        }
        
        .btn-success:hover {
            background-color: #27ae60;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .btn-danger:hover {
            background-color: #c0392b;
        }
        
        .btn-warning {
            background-color: var(--warning-color);
        }
        
        .btn-warning:hover {
            background-color: #d35400;
        }
        
        .btn-disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        
        .btn-disabled:hover {
            background-color: #7f8c8d;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }
        
        .alert-danger {
            background-color: rgba(231, 76, 60, 0.2);
            color: #c0392b;
        }
        
        .empty-state {
            text-align: center;
            padding: 50px 20px;
            color: #666;
            grid-column: 1 / -1;
        }
        
        .empty-state i {
            font-size: 40px;
            color: #ddd;
            margin-bottom: 10px;
        }
        
        .empty-state p {
            margin: 0;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
                margin: 20px auto;
            }
            
            .venues-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/studentsidebar.php'); ?>

<div class="container">
    <h2><i class="fas fa-building"></i> Venues</h2>
    
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <div class="filter-container">
        <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" id="venueSearch" placeholder="Search venues...">
        </div>
        
        <div class="filter-group">
            <span class="filter-label">Status:</span>
            <select id="statusFilter" class="filter-select">
                <option value="all">All</option>
                <option value="available">Available</option>
                <option value="occupied">Occupied</option>
                <option value="maintenance">Maintenance</option>
            </select>
        </div>
        
        <div class="filter-group">
            <span class="filter-label">Type:</span>
            <select id="typeFilter" class="filter-select">
                <option value="all">All</option>
                <option value="classroom">Classroom</option>
                <option value="lab">Laboratory</option>
                <option value="hall">Hall</option>
                <option value="office">Office</option>
                <option value="other">Other</option>
            </select>
        </div>
    </div>
    
    <div class="venues-grid" id="venuesGrid">
        <?php if (mysqli_num_rows($venues_result) > 0): ?>
            <?php while ($venue = mysqli_fetch_assoc($venues_result)): ?>
                <div class="venue-card" 
                     data-status="<?php echo htmlspecialchars($venue['status']); ?>"
                     data-type="<?php echo isset($venue['venuetype']) ? htmlspecialchars($venue['venuetype']) : 'other'; ?>">
                    <div class="venue-header">
                        <span class="venue-status status-<?php echo htmlspecialchars($venue['status']); ?>">
                            <?php echo ucfirst(htmlspecialchars($venue['status'])); ?>
                        </span>
                        <h3 class="venue-name"><?php echo htmlspecialchars($venue['venuename']); ?></h3>
                        <p class="venue-type">
                            <?php echo isset($venue['venuetype']) ? ucfirst(htmlspecialchars($venue['venuetype'])) : 'Other'; ?>
                        </p>
                    </div>
                    <div class="venue-body">
                        <?php if (!empty($venue['location'])): ?>
                            <div class="venue-info">
                                <i class="fas fa-map-marker-alt"></i>
                                <span><?php echo htmlspecialchars($venue['location']); ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($venue['capacity'])): ?>
                            <div class="venue-info">
                                <i class="fas fa-users"></i>
                                <span>Capacity: <?php echo htmlspecialchars($venue['capacity']); ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($venue['facilities'])): ?>
                            <div class="venue-info">
                                <i class="fas fa-tools"></i>
                                <span>Facilities: <?php echo htmlspecialchars($venue['facilities']); ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <div class="venue-actions">
                            <?php if (in_array($venue['venueid'], $active_venues)): ?>
                                <form method="post" action="process_venue_scan.php" style="width: 100%;">
                                    <input type="hidden" name="venue_id" value="<?php echo $venue['venueid']; ?>">
                                    <button type="submit" class="btn btn-danger btn-block">
                                        <i class="fas fa-sign-out-alt"></i> Check Out
                                    </button>
                                </form>
                            <?php elseif ($venue['status'] === 'available'): ?>
                                <a href="scan_venue.php" class="btn btn-success btn-block">
                                    <i class="fas fa-qrcode"></i> Scan to Check In
                                </a>
                            <?php else: ?>
                                <button class="btn btn-disabled btn-block" disabled>
                                    <i class="fas fa-ban"></i> Not Available
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endwhile; ?>
        <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-building"></i>
                <p>No venues found</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add active class to current page link
        const currentPage = window.location.pathname.split('/').pop();
        const menuLinks = document.querySelectorAll('#menu a');
        
        menuLinks.forEach(link => {
            const linkPage = link.getAttribute('href');
            if (linkPage === currentPage) {
                link.classList.add('active');
            }
        });
        
        // Venue filtering functionality
        const venueSearch = document.getElementById('venueSearch');
        const statusFilter = document.getElementById('statusFilter');
        const typeFilter = document.getElementById('typeFilter');
        const venuesGrid = document.getElementById('venuesGrid');
        const venueCards = document.querySelectorAll('.venue-card');
        
        function filterVenues() {
            const searchTerm = venueSearch.value.toLowerCase();
            const statusValue = statusFilter.value;
            const typeValue = typeFilter.value;
            
            let visibleCount = 0;
            
            venueCards.forEach(card => {
                const venueName = card.querySelector('.venue-name').textContent.toLowerCase();
                const venueStatus = card.dataset.status || '';
                const venueType = card.dataset.type || 'other';
                
                const matchesSearch = venueName.includes(searchTerm);
                const matchesStatus = statusValue === 'all' || venueStatus === statusValue;
                const matchesType = typeValue === 'all' || venueType === typeValue;
                
                if (matchesSearch && matchesStatus && matchesType) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });
            
            // Show empty state if no venues match filters
            const emptyState = document.querySelector('.empty-state');
            if (emptyState) {
                if (visibleCount === 0) {
                    emptyState.style.display = 'block';
                } else {
                    emptyState.style.display = 'none';
                }
            }
        }
        
        venueSearch.addEventListener('input', filterVenues);
        statusFilter.addEventListener('change', filterVenues);
        typeFilter.addEventListener('change', filterVenues);
    });
</script>

</body>
</html>



