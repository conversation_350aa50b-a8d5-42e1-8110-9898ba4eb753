<?php
session_start();

// Check if the user is logged in and is an admin
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    // Redirect unauthorized users to login page
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Initialize variables
$venue = null;
$error_message = "";
$checkins = [];

// Check if venue ID is provided
if (isset($_GET['id']) && !empty($_GET['id'])) {
    $venue_id = $_GET['id'];
    
    // Get venue details
    $sql = "SELECT * FROM venues WHERE venueid = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "i", $venue_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 1) {
        $venue = mysqli_fetch_assoc($result);
        
        // Get recent check-ins for this venue
        $checkin_sql = "SELECT vc.*, u.full_name, u.email 
                        FROM venue_checkins vc 
                        JOIN users u ON vc.user_id = u.user_id 
                        WHERE vc.venue_id = ? 
                        ORDER BY vc.check_in_time DESC 
                        LIMIT 10";
        $checkin_stmt = mysqli_prepare($conn, $checkin_sql);
        mysqli_stmt_bind_param($checkin_stmt, "i", $venue_id);
        mysqli_stmt_execute($checkin_stmt);
        $checkin_result = mysqli_stmt_get_result($checkin_stmt);
        
        while ($row = mysqli_fetch_assoc($checkin_result)) {
            $checkins[] = $row;
        }
    } else {
        $error_message = "Venue not found.";
    }
} else {
    $error_message = "Venue ID is required.";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>View Venue | Admin Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-dark);
        }
        
        .container {
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            margin: 20px;
            transition: margin-left 0.3s ease;
        }
        
        #menuToggle:checked ~ .container {
            margin-left: 270px;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .page-header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .card-header {
            background: var(--primary-light);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 500;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .venue-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .detail-group {
            margin-bottom: 20px;
        }
        
        .detail-group h3 {
            margin-top: 0;
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        .detail-item {
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
        }
        
        .detail-label {
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .status-free {
            background-color: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }
        
        .status-occupied {
            background-color: rgba(231, 76, 60, 0.2);
            color: #c0392b;
        }
        
        .feature-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        
        .feature-tag {
            background-color: rgba(52, 152, 219, 0.1);
            color: var(--primary-color);
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            margin: 5px;
        }
        
        .btn i {
            margin-right: 5px;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-warning {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-warning:hover {
            background: #d35400;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-success {
            background: var(--success-color);
            color: white;
        }
        
        .btn-success:hover {
            background: #27ae60;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background-color: rgba(231, 76, 60, 0.2);
            border: 1px solid var(--danger-color);
            color: #c0392b;
        }
        
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }
            
            .page-header {
                padding: 20px;
            }
            
            .page-header h1 {
                font-size: 24px;
            }
            
            .venue-details {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            #menuToggle:checked ~ .container {
                margin-left: 20px;
                opacity: 0.4;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/sidebar.php'); ?>

<div class="container">
    <div class="page-header">
        <h1>View Venue Details</h1>
        <p>Comprehensive information about the selected venue.</p>
    </div>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <?php if ($venue): ?>
        <div class="card">
            <div class="card-header">
                <i class="fas fa-building"></i> <?php echo htmlspecialchars($venue['venuename']); ?>
            </div>
            <div class="card-body">
                <div class="venue-details">
                    <div class="detail-group">
                        <h3>Basic Information</h3>
                        <div class="detail-item">
                            <span class="detail-label">Venue ID:</span>
                            <span><?php echo $venue['venueid']; ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Name:</span>
                            <span><?php echo htmlspecialchars($venue['venuename']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Location:</span>
                            <span><?php echo htmlspecialchars($venue['location']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Capacity:</span>
                            <span><?php echo $venue['capacity']; ?> people</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Status:</span>
                            <span class="status-badge status-<?php echo $venue['status']; ?>">
                                <?php echo ucfirst($venue['status']); ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="detail-group">
                        <h3>Additional Details</h3>
                        <?php if (!empty($venue['description'])): ?>
                            <div class="detail-item">
                                <span class="detail-label">Description:</span>
                                <span><?php echo htmlspecialchars($venue['description']); ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($venue['features'])): ?>
                            <div class="detail-item">
                                <span class="detail-label">Features:</span>
                                <div class="feature-list">
                                    <?php 
                                    $features = explode(',', $venue['features']);
                                    foreach ($features as $feature): 
                                        $feature = trim($feature);
                                        if (!empty($feature)):
                                    ?>
                                        <span class="feature-tag">
                                            <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($feature); ?>
                                        </span>
                                    <?php 
                                        endif;
                                    endforeach; 
                                    ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <a href="edit_venue.php?id=<?php echo $venue['venueid']; ?>" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit Venue
                    </a>
                    <a href="generate_qr.php?id=<?php echo $venue['venueid']; ?>" class="btn btn-success">
                        <i class="fas fa-qrcode"></i> Generate QR Code
                    </a>
                    <a href="venues.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Venues
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <i class="fas fa-history"></i> Recent Check-ins
            </div>
            <div class="card-body">
                <?php if (empty($checkins)): ?>
                    <p>No recent check-ins for this venue.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Check-in Time</th>
                                    <th>Check-out Time</th>
                                    <th>Purpose</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($checkins as $checkin): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($checkin['full_name']); ?></td>
                                        <td><?php echo date('Y-m-d H:i:s', strtotime($checkin['check_in_time'])); ?></td>
                                        <td>
                                            <?php 
                                            if (!empty($checkin['check_out_time'])) {
                                                echo date('Y-m-d H:i:s', strtotime($checkin['check_out_time']));
                                            } else {
                                                echo '<span class="status-badge status-occupied">Still checked in</span>';
                                            }
                                            ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($checkin['purpose'] ?? 'Not specified'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <div class="card">
            <div class="card-header">
                <i class="fas fa-exclamation-triangle"></i> No Venue Selected
            </div>
            <div class="card-body">
                <p>Please select a venue to view details.</p>
                <a href="venues.php" class="btn btn-primary">
                    <i class="fas fa-list"></i> View Venues
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>

</body>
</html>