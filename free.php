<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "lecturer") {
    // Redirect unauthorized users to another page, maybe the dashboard or homepage
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Initialize search variables
$search_term = "";
$search_condition = "";

// Handle search
if (isset($_POST['search'])) {
    $search_term = mysqli_real_escape_string($conn, $_POST['search_term']);
    $search_condition = " AND (venuename LIKE '%$search_term%' OR venuedescription LIKE '%$search_term%')";
}

// Clear search
if (isset($_POST['clear_search'])) {
    $search_term = "";
    $search_condition = "";
}

// Fetch all free venues
$sqlFreeVenues = "SELECT * FROM venues WHERE status = 'free'" . $search_condition;
$resultFreeVenues = mysqli_query($conn, $sqlFreeVenues);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <style>
        .venues-container {
            max-width: 1200px;
            margin: 30px auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .search-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .search-form {
            display: flex;
            gap: 15px;
            align-items: center;
            max-width: 100%;
            margin: 0;
            padding: 0;
            box-shadow: none;
        }
        
        .search-input {
            flex: 1;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .search-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .search-btn:hover {
            background: var(--primary-dark);
        }
        
        .venues-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
        }
        
        .venue-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .venue-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .venue-header {
            background: var(--primary-light);
            color: white;
            padding: 15px 20px;
        }
        
        .venue-header h2 {
            margin: 0;
            font-size: 20px;
            font-weight: 500;
        }
        
        .venue-body {
            padding: 20px;
        }
        
        .venue-description {
            color: var(--text-dark);
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .venue-status {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            background: rgba(46, 204, 113, 0.2);
            color: var(--success-color);
        }
        
        .venue-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }
        
        .venue-btn {
            padding: 8px 15px;
            border-radius: 4px;
            font-size: 14px;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s;
        }
        
        .venue-btn-reserve {
            background-color: var(--primary-color);
            color: white;
        }
        
        .venue-btn-reserve:hover {
            background-color: var(--primary-dark);
        }
        
        .no-venues {
            text-align: center;
            padding: 40px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .no-venues i {
            font-size: 48px;
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .no-venues h2 {
            color: var(--text-dark);
            margin-bottom: 10px;
        }
        
        .no-venues p {
            color: #777;
        }
    </style>
</head>
<body>

<?php include('CSS/lecturersidebar.php'); ?>

<div class="container venues-container">
    <div class="page-header">
        <h1><i class="fas fa-building"></i> Available Free Venues</h1>
    </div>
    
    <div class="search-container">
        <form action="free.php" method="post" class="search-form">
            <input type="text" name="search_term" class="search-input" placeholder="Search by venue name or description" value="<?php echo htmlspecialchars($search_term); ?>">
            <button type="submit" name="search" class="search-btn">
                <i class="fas fa-search"></i> Search
            </button>
            <?php if (!empty($search_term)): ?>
                <button type="submit" name="clear_search" class="search-btn" style="background-color: #777;">
                    <i class="fas fa-times"></i> Clear Search
                </button>
            <?php endif; ?>
        </form>
    </div>
    
    <?php if (mysqli_num_rows($resultFreeVenues) > 0): ?>
        <div class="venues-grid">
            <?php while ($row = mysqli_fetch_assoc($resultFreeVenues)): ?>
                <div class="venue-card">
                    <div class="venue-header">
                        <h2><?php echo htmlspecialchars($row['venuename']); ?></h2>
                    </div>
                    <div class="venue-body">
                        <p class="venue-description">
                            <?php 
                                $description = htmlspecialchars($row['venuedescription']);
                                echo !empty($description) ? $description : "No description available.";
                            ?>
                        </p>
                        <div class="venue-status">
                            <i class="fas fa-check-circle"></i> Available
                        </div>
                        <div class="venue-actions">
                            <a href="reserve_venue.php?id=<?php echo $row['id']; ?>" class="venue-btn venue-btn-reserve">
                                <i class="fas fa-calendar-check"></i> Reserve
                            </a>
                        </div>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>
    <?php else: ?>
        <div class="no-venues">
            <i class="fas fa-search"></i>
            <h2>No Free Venues Found</h2>
            <p>There are currently no available venues matching your search criteria.</p>
        </div>
    <?php endif; ?>
</div>

<?php include('CSS/footer.php'); ?>
</body>
</html>
