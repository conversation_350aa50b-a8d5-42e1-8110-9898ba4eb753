<?php
session_start();

// Check if the user is logged in and is a student
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "student") {
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Get user details
$user_id = $_SESSION["user"];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Mark Attendance | Student Dashboard</title>
    <style>
        /* Use the same styles as scan_venue.php */
    </style>
    <!-- Include the HTML5 QR Code Scanner library -->
    <script src="https://unpkg.com/html5-qrcode"></script>
</head>
<body>

<?php include('CSS/studentsidebar.php'); ?>

<div class="container">
    <h2><i class="fas fa-clipboard-check"></i> Mark Attendance</h2>
    
    <div class="scan-container">
        <div class="scan-header">
            <p>Scan the venue QR code to mark your attendance for this class.</p>
            <p class="info-text">Note: A lecturer must be checked in at the venue before you can mark attendance.</p>
        </div>
        
        <div id="qr-reader"></div>
        
        <div id="result-container">
            <h3>Success!</h3>
            <p id="result-message"></p>
            <a href="student_dashboard.php" class="btn">Return to Dashboard</a>
        </div>
        
        <div id="error-container">
            <h3>Error</h3>
            <p id="error-message"></p>
            <button onclick="resetScanner()" class="btn">Try Again</button>
        </div>
        
        <div class="manual-entry">
            <h3>Manual Entry</h3>
            <p>If scanning doesn't work, you can enter the venue code manually:</p>
            
            <form id="manual-form" method="post" action="process_venue_scan.php">
                <div class="form-group">
                    <label for="venue_code">Venue Code</label>
                    <input type="text" id="venue_code" name="venue_code" class="form-control" placeholder="Enter venue code" required>
                </div>
                
                <button type="submit" class="btn btn-block">Mark Attendance</button>
            </form>
        </div>
    </div>
</div>

<script>
    // Same JavaScript as scan_venue.php
</script>

</body>
</html>