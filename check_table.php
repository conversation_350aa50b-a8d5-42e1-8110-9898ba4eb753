<?php
require_once "database.php";

// Check if login_logs table exists
$check_table = "SHOW TABLES LIKE 'login_logs'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) > 0) {
    echo "The login_logs table exists.";
} else {
    echo "The login_logs table does NOT exist.";
}

// Show all tables in the database
echo "<br><br>All tables in the database:<br>";
$all_tables = "SHOW TABLES";
$tables_result = mysqli_query($conn, $all_tables);

while ($row = mysqli_fetch_row($tables_result)) {
    echo $row[0] . "<br>";
}
?>