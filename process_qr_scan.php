<?php
// Include database connection
require_once "database.php";

// Check if the scanned data is present in the POST request
if(isset($_POST['scannedData'])){
    // Get the scanned data
    $scannedData = $_POST['scannedData'];

    // Process the scanned data (You might want to decode it if it's in JSON format)
    $decodedData = json_decode($scannedData, true);

    // Assuming the venue name is present in the scanned data
    if(isset($decodedData['venuename'])){ 
        $venueName = $decodedData['venuename'];

        // Update the venue status to 'occupied' in the database
        $updateSql = "UPDATE venues SET status = 'occupied' WHERE venuename = ?";
        $updateStmt = mysqli_prepare($conn, $updateSql);

        if ($updateStmt) {
            mysqli_stmt_bind_param($updateStmt, "s", $venueName);
            mysqli_stmt_execute($updateStmt);

            // Send a response back to the client (if needed)
            echo json_encode(['success' => true, 'message' => 'Venue status updated successfully']);
        } else {
            // Send an error response back to the client (if needed)
            echo json_encode(['success' => false, 'message' => 'Error updating venue status']);
        }
    } else {
        // Send an error response back to the client (if needed)
        echo json_encode(['success' => false, 'message' => 'Invalid scanned data']);
    }
} else {
    // Send an error response back to the client (if needed)
    echo json_encode(['success' => false, 'message' => 'Scanned data not present']);
}

// Close the database connection
mysqli_close($conn);
?>
