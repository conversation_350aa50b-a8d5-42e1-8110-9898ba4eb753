<?php
require_once "database.php";

// Create login_logs table
$create_login_logs = "
CREATE TABLE IF NOT EXISTS login_logs (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    login_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    logout_time DATETIME DEFAULT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    status VARCHAR(20) DEFAULT 'success',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
";

if (mysqli_query($conn, $create_login_logs)) {
    echo "login_logs table created successfully.<br>";
} else {
    echo "Error creating login_logs table: " . mysqli_error($conn) . "<br>";
}

// Create issues table
$create_issues = "
CREATE TABLE IF NOT EXISTS issues (
    issue_id INT AUTO_INCREMENT PRIMARY KEY,
    venue_id INT,
    user_id INT,
    issue_title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    issue_type VARCHAR(50) NOT NULL,
    status ENUM('pending', 'in_progress', 'resolved', 'closed') DEFAULT 'pending',
    reported_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    resolved_date DATETIME DEFAULT NULL,
    resolved_by INT DEFAULT NULL,
    resolution_notes TEXT DEFAULT NULL,
    FOREIGN KEY (venue_id) REFERENCES venues(venueid) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
";

if (mysqli_query($conn, $create_issues)) {
    echo "issues table created successfully.<br>";
} else {
    echo "Error creating issues table: " . mysqli_error($conn) . "<br>";
}

echo "Done.";
?>